-- Budget Management Functions
-- This file contains functions for managing budget operations, snapshots, and imports
-- Function to create budget snapshots
CREATE OR REPLACE FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text" DEFAULT NULL::"text"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
BEGIN 
	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN 
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;
	
	FOR v_item IN (
		SELECT *
		FROM public.budget_line_item_current
		WHERE project_id = v_project_id
	) LOOP
		INSERT INTO public.budget_snapshot_line_item (
			budget_snapshot_id,
			wbs_library_item_id,
			quantity,
			unit,
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			remarks,
			cost_certainty,
			design_certainty
		)
		VALUES (
			v_snapshot_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty
		);
	END LOOP;
	
	RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) IS 'Creates a snapshot of the current budget state for a project stage';

-- Function to revert to a budget snapshot
CREATE OR REPLACE FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text" DEFAULT 'Reverted to snapshot'::"text"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_project_id UUID;
	v_item RECORD;
BEGIN
	-- Get the project_id from the snapshot and stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
	WHERE bs.budget_snapshot_id = p_budget_snapshot_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
	END IF;

	FOR v_item IN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_budget_snapshot_id
	) LOOP
		-- Use the upsert function for each item
		PERFORM public.upsert_budget_line_item(
			v_project_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty,
			p_revert_reason,
			NULL -- We want to create new records when reverting, not update existing ones
		);
	END LOOP;

	RETURN TRUE;
END;
$$;

ALTER FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) IS 'Reverts the current budget to a previous snapshot state';

-- Function to upsert budget line items
CREATE OR REPLACE FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text" DEFAULT NULL::"text",
	"p_material_rate" numeric DEFAULT 0,
	"p_labor_rate" numeric DEFAULT NULL::numeric,
	"p_productivity_per_hour" numeric DEFAULT NULL::numeric,
	"p_unit_rate_manual_override" boolean DEFAULT false,
	"p_unit_rate" numeric DEFAULT 0,
	"p_factor" numeric DEFAULT NULL::numeric,
	"p_remarks" "text" DEFAULT NULL::"text",
	"p_cost_certainty" numeric DEFAULT NULL::numeric,
	"p_design_certainty" numeric DEFAULT NULL::numeric,
	"p_change_reason" "text" DEFAULT NULL::"text",
	"p_budget_line_item_id" "uuid" DEFAULT NULL::"uuid"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_budget_line_item_id UUID;
	v_cost_to_use NUMERIC;
BEGIN
	-- Determine which cost to use
	IF p_unit_rate_manual_override THEN
		v_cost_to_use := p_unit_rate;
	ELSE
		v_cost_to_use := COALESCE(p_material_rate, 0) +
			CASE
				WHEN p_labor_rate IS NOT NULL AND p_productivity_per_hour IS NOT NULL
				THEN p_labor_rate / p_productivity_per_hour
				ELSE 0
			END;
	END IF;

	-- If budget_line_item_id is provided, try to update existing record
	IF p_budget_line_item_id IS NOT NULL THEN
		UPDATE public.budget_line_item_current
		SET
			quantity = p_quantity,
			unit = p_unit,
			material_rate = p_material_rate,
			labor_rate = p_labor_rate,
			productivity_per_hour = p_productivity_per_hour,
			unit_rate_manual_override = p_unit_rate_manual_override,
			unit_rate = v_cost_to_use,
			factor = p_factor,
			remarks = p_remarks,
			cost_certainty = p_cost_certainty,
			design_certainty = p_design_certainty,
			updated_at = now()
		WHERE budget_line_item_id = p_budget_line_item_id
		RETURNING budget_line_item_id INTO v_budget_line_item_id;

		IF FOUND THEN
			RETURN v_budget_line_item_id;
		END IF;

	-- Insert new record
	INSERT INTO public.budget_line_item_current (
		project_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
	VALUES (
		p_project_id,
		p_wbs_library_item_id,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	)
	ON CONFLICT (project_id, wbs_library_item_id) DO UPDATE SET
		quantity = EXCLUDED.quantity,
		unit = EXCLUDED.unit,
		material_rate = EXCLUDED.material_rate,
		labor_rate = EXCLUDED.labor_rate,
		productivity_per_hour = EXCLUDED.productivity_per_hour,
		unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
		unit_rate = EXCLUDED.unit_rate,
		factor = EXCLUDED.factor,
		remarks = EXCLUDED.remarks,
		cost_certainty = EXCLUDED.cost_certainty,
		design_certainty = EXCLUDED.design_certainty,
		updated_at = now()
	RETURNING budget_line_item_id INTO v_budget_line_item_id;

	RETURN v_budget_line_item_id;
END;
$$;

ALTER FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) IS 'Inserts or updates a budget line item with calculated unit rates';

END IF;
