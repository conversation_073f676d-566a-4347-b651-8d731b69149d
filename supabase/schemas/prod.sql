SET
	statement_timeout = 0;

SET
	lock_timeout = 0;

SET
	idle_in_transaction_session_timeout = 0;

SET
	client_encoding = 'UTF8';

SET
	standard_conforming_strings = on;

SELECT
	pg_catalog.set_config ('search_path', '', false);

SET
	check_function_bodies = false;

SET
	xmloption = content;

SET
	client_min_messages = warning;

SET
	row_security = off;

CREATE EXTENSION IF NOT EXISTS "pg_net"
WITH
	SCHEMA "extensions";

COMMENT ON SCHEMA "public" IS 'standard public schema';

CREATE EXTENSION IF NOT EXISTS "pg_graphql"
WITH
	SCHEMA "graphql";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgcrypto"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgjwt"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "supabase_vault"
WITH
	SCHEMA "vault";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
WITH
	SCHEMA "extensions";

CREATE TYPE "public"."checklist_item_status" AS ENUM('Incomplete', 'Deferred', 'Complete');

ALTER TYPE "public"."checklist_item_status" OWNER TO "postgres";

CREATE TYPE "public"."entity_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."entity_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_resource_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."invite_resource_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_status" AS ENUM(
	'pending',
	'accepted',
	'revoked',
	'expired',
	'declined'
);

ALTER TYPE "public"."invite_status" OWNER TO "postgres";

CREATE TYPE "public"."membership_role" AS ENUM('viewer', 'editor', 'admin', 'owner');

ALTER TYPE "public"."membership_role" OWNER TO "postgres";

CREATE TYPE "public"."wbs_item_type" AS ENUM('Standard', 'Custom');

ALTER TYPE "public"."wbs_item_type" OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."accept_invite" ("token_param" character) RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare v_invite public.invite %rowtype;
v_user_id uuid;
v_entity_type public.entity_type;
v_entity_id uuid;
v_role public.membership_role;
v_resource_type text;
begin -- Get the current user ID
v_user_id := auth.uid();
if v_user_id is null then raise exception 'Not authenticated';
end if;
select * into v_invite
from public.invite
where token_hash = token_param
	and status = 'pending'
	and expires_at > now();
if not found then raise exception 'Invalid or expired invite token';
end if;
v_resource_type := v_invite.resource_type::text;
if v_resource_type = 'organization' then v_entity_type := 'organization'::public.entity_type;
v_entity_id := v_invite.resource_id;
if v_invite.role = 'member' then v_role := 'viewer'::public.membership_role;
else v_role := v_invite.role::public.membership_role;
end if;
elsif v_resource_type = 'client' then v_entity_type := 'client'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
elsif v_resource_type = 'project' then v_entity_type := 'project'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
else raise exception 'Invalid resource type: %',
v_invite.resource_type;
end if;
insert into public.membership(user_id, role, entity_type, entity_id)
values (v_user_id, v_role, v_entity_type, v_entity_id) on conflict (entity_type, entity_id, user_id) do nothing;
update public.invite
set status = 'accepted'::public.invite_status,
	updated_at = now(),
	updated_by = v_user_id
where invite_id = v_invite.invite_id;
return json_build_object(
	'success',
	true,
	'message',
	'Invite accepted successfully',
	'resource_type',
	v_invite.resource_type,
	'resource_id',
	v_invite.resource_id
);
exception
when others then return json_build_object('success', false, 'message', SQLERRM);
end;
$$;

ALTER FUNCTION "public"."accept_invite" ("token_param" character) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."add_creator_as_admin" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare has_org_admin_access boolean := false;
org_id_var uuid;
begin -- Add the creator as an admin/owner
if TG_TABLE_NAME = 'organization' then -- For organizations, simply add the creator as admin
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'organization',
		NEW.org_id
	);
elsif TG_TABLE_NAME = 'client' then -- For clients, check if the creator already has admin access through the organization
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = NEW.org_id
			and m.role = 'admin'
	) into has_org_admin_access;
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'client',
		NEW.client_id
	);
end if;
elsif TG_TABLE_NAME = 'project' then -- For projects, get the client's org_id
select c.org_id into org_id_var
from public.client c
where c.client_id = NEW.client_id;
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = org_id_var
			and m.role = 'admin'
	) into has_org_admin_access;
if has_org_admin_access then -- Skip adding project owner membership
null;
else -- Check if they have admin access through the client
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'client'
			and m.entity_id = NEW.client_id
			and m.role = 'admin'
	) into has_org_admin_access;
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'owner',
		'project',
		NEW.project_id
	);
end if;
end if;
end if;
return NEW;
end;
$$;

ALTER FUNCTION "public"."add_creator_as_admin" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."add_creator_as_admin" () IS 'Automatically adds the creator as an admin/owner of the entity';

CREATE OR REPLACE FUNCTION "public"."apply_pending_invites" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare v_invite public.invite %rowtype;
v_entity_type public.entity_type;
v_entity_id uuid;
v_role public.membership_role;
v_resource_type text;
begin -- Loop through all pending invites for this email
begin for v_invite in (
	select *
	from public.invite
	where lower(invitee_email) = lower(new.email)
		and status = 'pending'
		and expires_at > now()
) loop begin -- Store resource_type as text to avoid casting issues
v_resource_type := v_invite.resource_type::text;
if v_resource_type = 'organization' then v_entity_type := 'organization'::public.entity_type;
v_entity_id := v_invite.resource_id;
if v_invite.role = 'member' then v_role := 'viewer'::public.membership_role;
else v_role := v_invite.role::public.membership_role;
end if;
elsif v_resource_type = 'client' then v_entity_type := 'client'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
elsif v_resource_type = 'project' then v_entity_type := 'project'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
else continue;
end if;
begin
insert into public.membership(user_id, role, entity_type, entity_id)
values (new.user_id, v_role, v_entity_type, v_entity_id) on conflict (entity_type, entity_id, user_id) do nothing;
update public.invite
set status = 'accepted'::public.invite_status,
	updated_at = now(),
	updated_by = new.user_id
where invite_id = v_invite.invite_id;
exception
when others then -- Log error but continue processing other invites
raise notice 'Error processing invite %: %',
v_invite.invite_id,
SQLERRM;
end;
exception
when others then -- Log error but continue processing other invites
raise notice 'Error mapping invite %: %',
v_invite.invite_id,
SQLERRM;
end;
end loop;
begin
update public.invite
set status = 'expired'::public.invite_status,
	updated_at = now(),
	updated_by = new.user_id
where lower(invitee_email) = lower(new.email)
	and status = 'pending'
	and expires_at <= now();
exception
when others then -- Log error but allow user creation to continue
raise notice 'Error expiring outdated invites: %',
SQLERRM;
end;
exception
when others then -- Log error but allow user creation to continue
raise notice 'Error in apply_pending_invites: %',
SQLERRM;
end;
return new;
end;
$$;

ALTER FUNCTION "public"."apply_pending_invites" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_approved_changes_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.approved_change_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.date_approved, OLD.cause, OLD.effect, OLD.program_impact, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.approved_by_user_id, OLD.original_risk_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_approved_changes_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_budget_line_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_line_item_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_gateway_checklist_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.gateway_checklist_item_id, OLD.project_stage_id, OLD.name, OLD.description,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_gateway_checklist_item_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_project_gateway_stage_info_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.project_gateway_stage_info_id, OLD.project_stage_id, OLD.basement_floors, OLD.ground_floor, OLD.upper_floors,
            OLD.total_gross_internal_floor_area, OLD.usable_area, OLD.circulation_area, OLD.ancillary_areas, OLD.internal_divisions,
            OLD.spaces_not_enclosed, OLD.total_gross_internal_floor_area_2, OLD.internal_cube, OLD.area_of_lowest_floor,
            OLD.site_area, OLD.number_of_units, OLD.nr_of_storeys, OLD.nr_of_storeys_primary, OLD.nr_of_storeys_secondary,
            OLD.basement_storeys_included_above, OLD.average_storey_height, OLD.below_ground_floors, OLD.ground_floor_height,
            OLD.above_ground_floors, OLD.external_vertical_envelope, OLD.additional_data, OLD.created_by_user_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_gateway_stage_info_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_project_stage_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_stage_id, OLD.project_id, OLD.name, OLD.description, OLD.stage_order, OLD.stage,
            OLD.gateway_qualitative_scorecard, OLD.date_started, OLD.date_completed, OLD.completion_notes,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSE
        RAISE EXCEPTION 'Unexpected TG_OP value: %', TG_OP;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_stage_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_risk_register_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.probability, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_risk_register_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."audit_wbs_library_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like seed data or system operations
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.wbs_library_item_id, OLD.wbs_library_id, OLD.level, OLD.in_level_code, OLD.parent_item_id,
            OLD.code, OLD.description, OLD.cost_scope, OLD.item_type::TEXT, OLD.client_id, OLD.project_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type::TEXT, NEW.client_id, NEW.project_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type::TEXT, NEW.client_id, NEW.project_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_wbs_library_item_changes" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) RETURNS numeric LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE v_cost NUMERIC;
BEGIN -- Simple calculation for now - can be enhanced later
v_cost := COALESCE(p_material_rate, 0);
IF p_labor_rate IS NOT NULL
AND p_productivity_per_hour IS NOT NULL
AND p_productivity_per_hour > 0 THEN v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
END IF;
RETURN v_cost;
END;
$$;

ALTER FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_access_client" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_access('client', client_id_param);
end;
$$;

ALTER FUNCTION "public"."can_access_client" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_access_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_access('project', project_id_param);
end;
$$;

ALTER FUNCTION "public"."can_access_project" ("project_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_role('client', client_id_param, 'editor');
end;
$$;

ALTER FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$;

ALTER FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_role('project', project_id_param, 'editor');
end;
$$;

ALTER FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."check_membership_redundancy" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare ancestor_role public.membership_role;
begin -- Check if user already has equal or higher role through an ancestor
if NEW.entity_type = 'project' then -- Check client level
select public.get_effective_role(NEW.user_id, 'client', p.client_id) into ancestor_role
from public.project p
where p.project_id = NEW.entity_id;
if ancestor_role = 'admin'
or ancestor_role = 'owner' then raise exception 'User already has admin access to this project through client-level permissions';
end if;
select public.get_effective_role(NEW.user_id, 'organization', c.org_id) into ancestor_role
from public.project p
	join public.client c on p.client_id = c.client_id
where p.project_id = NEW.entity_id;
if ancestor_role = 'admin' then raise exception 'User already has admin access to this project through organization-level permissions';
end if;
end if;
if NEW.entity_type = 'client' then -- Check organization level
select public.get_effective_role(NEW.user_id, 'organization', c.org_id) into ancestor_role
from public.client c
where c.client_id = NEW.entity_id;
if ancestor_role = 'admin' then raise exception 'User already has admin access to this client through organization-level permissions';
end if;
end if;
return NEW;
end;
$$;

ALTER FUNCTION "public"."check_membership_redundancy" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."check_membership_redundancy" () IS 'Prevents redundant memberships when a user already has access through an ancestor entity';

CREATE OR REPLACE FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) RETURNS TABLE (
	"wbs_library_item_id" "uuid",
	"snapshot_1_quantity" numeric,
	"snapshot_1_cost" numeric,
	"snapshot_2_quantity" numeric,
	"snapshot_2_cost" numeric,
	"quantity_diff" numeric,
	"cost_diff" numeric,
	"percent_change" numeric
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ BEGIN RETURN QUERY
SELECT COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
	s1.quantity AS snapshot_1_quantity,
	s1.unit_rate AS snapshot_1_cost,
	s1.factor AS snapshot_1_factor,
	s2.quantity AS snapshot_2_quantity,
	s2.unit_rate AS snapshot_2_cost,
	s2.factor AS snapshot_2_factor,
	COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
	COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
	CASE
		WHEN COALESCE(s1.unit_rate, 0) = 0 THEN CASE
			WHEN COALESCE(s2.unit_rate, 0) = 0 THEN 0
			ELSE NULL -- Can't calculate percent change from zero
		END
		ELSE (
			COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)
		) / COALESCE(s1.unit_rate, 1) * COALESCE(s1.factor, 1) / COALESCE(s2.factor, 1) * 100
	END AS percent_change
FROM (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_1
	) s1
	FULL OUTER JOIN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_2
	) s2 ON s1.wbs_library_item_id = s2.wbs_library_item_id;
END;
$$;

ALTER FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text" DEFAULT NULL::"text"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE v_snapshot_id UUID;
v_is_ready BOOLEAN;
BEGIN -- Check if all checklist items are completed
SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
IF NOT v_is_ready THEN RAISE EXCEPTION 'Cannot complete stage: not all checklist items are completed';
END IF;
SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;
RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text" DEFAULT NULL::"text"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE v_project_id UUID;
v_snapshot_id UUID;
v_item RECORD;
BEGIN -- Get the project_id from the stage
SELECT project_id INTO v_project_id
FROM public.project_stage
WHERE project_stage_id = p_project_stage_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Project stage not found';
END IF;
INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
RETURNING budget_snapshot_id INTO v_snapshot_id;
FOR v_item IN (
	SELECT *
	FROM public.budget_line_item_current
	WHERE project_id = v_project_id
) LOOP
INSERT INTO public.budget_snapshot_line_item (
		budget_snapshot_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		v_snapshot_id,
		v_item.wbs_library_item_id,
		v_item.quantity,
		v_item.unit,
		v_item.material_rate,
		v_item.labor_rate,
		v_item.productivity_per_hour,
		v_item.unit_rate_manual_override,
		v_item.unit_rate,
		v_item.factor,
		v_item.remarks,
		v_item.cost_certainty,
		v_item.design_certainty
	);
END LOOP;
UPDATE public.project_stage
SET date_completed = now()
WHERE project_stage_id = p_project_stage_id;
RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text" DEFAULT NULL::"text",
	"logo_url" "text" DEFAULT NULL::"text"
) RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare new_org public.organization;
user_id uuid := auth.uid();
begin -- Ensure user is authenticated
if user_id is null then raise exception 'Not authenticated';
end if;
insert into public.organization(name, description, logo_url, created_by_user_id)
values (name, description, logo_url, user_id)
returning * into new_org;
return json_build_object(
	'org_id',
	new_org.org_id,
	'name',
	new_org.name,
	'description',
	new_org.description,
	'logo_url',
	new_org.logo_url
);
end;
$$;

ALTER FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) IS 'Creates a new organization and adds the creator as an admin';

CREATE OR REPLACE FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
end;
$$;

ALTER FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if the current user has access to an entity';

CREATE OR REPLACE FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.has_entity_role(
		auth.uid(),
		entity_type_param,
		entity_id_param,
		min_role_param
	);
end;
$$;

ALTER FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) IS 'Checks if the current user has a specific role or higher on an entity';

CREATE OR REPLACE FUNCTION "public"."generate_demo_project_data" () RETURNS "jsonb" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $_$
DECLARE
    v_project_id UUID := gen_random_uuid();
    v_client_id UUID;
    v_wbs_library_id UUID;
		v_project_name TEXT := 'Test Project ' || now()::text;
    v_user_id UUID := auth.uid();
    stage1_id UUID;
    stage2_id UUID;
    stage3_id UUID;
    stage4_id UUID;
    snap1_id UUID;
    snap2_id UUID;
    snap3_id UUID;
    snap4_id UUID;
    item2 RECORD;
    item3 RECORD;
    item4 RECORD;
    stage_total NUMERIC;
    child_count INT;
    base_share NUMERIC;
    portion NUMERIC;
    remaining NUMERIC;
    idx INT;
    v_existing_project_count INT;
    -- Risk register variables
    v_risk_count INT;
    v_risk_id UUID;
    v_wbs_item_id UUID;
    v_risk_titles TEXT[] := ARRAY[
        'Weather delays during construction',
        'Material cost escalation',
        'Skilled labor shortage',
        'Regulatory approval delays',
        'Site access restrictions',
        'Utility relocation required',
        'Ground conditions worse than expected',
        'Design changes requested by client',
        'Supply chain disruptions',
        'Environmental compliance issues',
        'Contractor insolvency risk',
        'Technology integration challenges',
        'Quality control failures',
        'Safety incident potential',
        'Budget overrun risk',
        'Schedule compression required',
        'Stakeholder approval delays',
        'Third-party coordination issues',
        'Equipment failure risk',
        'Currency exchange fluctuations',
        'Political/regulatory changes',
        'Force majeure events',
        'Resource availability constraints',
        'Technical specification changes',
        'Market demand fluctuations'
    ];
    v_risk_statuses TEXT[] := ARRAY['identified', 'assessed', 'mitigated', 'occurred', 'closed'];
    v_approved_statuses TEXT[] := ARRAY['approved', 'implemented', 'closed'];
    v_causes TEXT[] := ARRAY[
        'External market conditions',
        'Regulatory requirements',
        'Technical constraints',
        'Resource limitations',
        'Client requirements',
        'Environmental factors',
        'Economic conditions',
        'Supplier dependencies',
        'Technology limitations',
        'Stakeholder decisions'
    ];
    v_effects TEXT[] := ARRAY[
        'Schedule delays',
        'Cost increases',
        'Quality impacts',
        'Resource reallocation',
        'Scope changes',
        'Performance degradation',
        'Compliance issues',
        'Stakeholder dissatisfaction',
        'Revenue loss',
        'Reputation damage'
    ];
    v_program_impacts TEXT[] := ARRAY[
        '1-2 week delay',
        '2-4 week delay',
        '1-2 month delay',
        'No schedule impact',
        'Minor schedule adjustment',
        'Significant milestone shift',
        'Critical path impact',
        'Phase completion delay',
        'Project completion delay',
        'Parallel work stream impact'
    ];
    v_mitigation_plans TEXT[] := ARRAY[
        'Implement contingency plan and monitor closely',
        'Engage alternative suppliers and negotiate contracts',
        'Increase resource allocation and accelerate timeline',
        'Establish regular stakeholder communication',
        'Develop technical workaround solutions',
        'Create buffer time in project schedule',
        'Implement quality assurance protocols',
        'Establish risk monitoring dashboard',
        'Negotiate contract amendments',
        'Deploy additional project management resources'
    ];
BEGIN
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to generate demo budget data';
    END IF;

    -- Look up client inserted in data.sql
    SELECT client_id INTO v_client_id FROM public.client WHERE name='Unity' LIMIT 1;

    IF v_client_id IS NULL THEN
        RAISE EXCEPTION 'Unity client not found. Please ensure seed data is loaded.';
    END IF;

    -- Look up WBS library ID for ICMS v3
    SELECT wbs_library_id INTO v_wbs_library_id FROM public.wbs_library WHERE name='ICMS v3' LIMIT 1;

    IF v_wbs_library_id IS NULL THEN
        RAISE EXCEPTION 'ICMS v3 WBS library not found. Please ensure seed data is loaded.';
    END IF;

    -- Create project
    INSERT INTO public.project (project_id, name, description, client_id, wbs_library_id, created_by_user_id)
    VALUES (v_project_id, v_project_name, 'Seeded test project', v_client_id, v_wbs_library_id, v_user_id);

    -- Create four stages
    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 1 - Concept', 1, 1)
    RETURNING project_stage_id INTO stage1_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 2 - Design', 2, 2)
    RETURNING project_stage_id INTO stage2_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 3 - Tender', 3, 3)
    RETURNING project_stage_id INTO stage3_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 4 - Construction', 4, 4)
    RETURNING project_stage_id INTO stage4_id;

    -- Stage 1 budget across level 2 items
    FOR item2 IN
        SELECT wbs_library_item_id FROM public.wbs_library_item WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3') AND level = 2
    LOOP
        portion := ROUND((75000000 + RANDOM() * 50000000)::NUMERIC, 2);
        INSERT INTO public.budget_line_item_current (
            project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override
        ) VALUES (
            v_project_id, item2.wbs_library_item_id, 1, portion, portion, FALSE
        );
    END LOOP;

    -- Snapshot after stage 1
    SELECT public.create_budget_snapshot(stage1_id, 'Stage 1 budget') INTO snap1_id;

    -- Prepare for stage 2
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item2 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap1_id
    LOOP
        stage_total := item2.unit_rate * (1 + (RANDOM() * 0.5 - 0.25));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item2.wbs_library_item_id, 1, ROUND(stage_total::NUMERIC, 2), ROUND(stage_total::NUMERIC, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item3 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND((base_share * (0.75 + RANDOM() * 0.5))::NUMERIC, 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining::NUMERIC, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item3.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage2_id, 'Stage 2 budget') INTO snap2_id;

    -- Stage 3: spread to level 4 with +/-15%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item3 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap2_id
    LOOP
        stage_total := item3.unit_rate * (1 + (RANDOM() * 0.3 - 0.15));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item3.wbs_library_item_id, 1, ROUND(stage_total::NUMERIC, 2), ROUND(stage_total::NUMERIC, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item4 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND((base_share * (0.85 + RANDOM() * 0.3))::NUMERIC, 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining::NUMERIC, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage3_id, 'Stage 3 budget') INTO snap3_id;

    -- Stage 4: adjust level 4 values +/-5%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item4 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap3_id
    LOOP
        portion := ROUND((item4.unit_rate * (1 + (RANDOM() * 0.1 - 0.05)))::NUMERIC, 2);
        INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
        VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
    END LOOP;

    SELECT public.create_budget_snapshot(stage4_id, 'Stage 4 budget') INTO snap4_id;

    -- Clear existing risk register and approved changes data for this project
    DELETE FROM public.approved_changes WHERE project_id = v_project_id;
    DELETE FROM public.risk_register WHERE project_id = v_project_id;

    -- Generate risk register entries (15-50 risks per project)
    v_risk_count := 15 + FLOOR(RANDOM() * 36)::INT;

    FOR idx IN 1..v_risk_count LOOP
        -- Select a random WBS item (about 70% of risks will have WBS linkage)
        v_wbs_item_id := NULL;
        IF RANDOM() < 0.7 THEN
            SELECT wbs_library_item_id
            INTO v_wbs_item_id
            FROM public.wbs_library_item
            WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
            AND level >= 2
            ORDER BY RANDOM()
            LIMIT 1;
        END IF;

        -- Insert risk with realistic data
        INSERT INTO public.risk_register (
            project_id,
            title,
            description,
            status,
            wbs_library_item_id,
            date_identified,
            cause,
            effect,
            program_impact,
            probability,
            potential_impact,
            mitigation_plan,
            date_for_review,
            risk_owner_user_id
        ) VALUES (
            v_project_id,
            v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT],
            'Detailed risk description for ' || v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT] ||
            '. This risk requires careful monitoring and may impact project delivery if not properly managed.',
            v_risk_statuses[1 + FLOOR(RANDOM() * array_length(v_risk_statuses, 1))::INT],
            v_wbs_item_id,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 90)::INT,
            v_causes[1 + FLOOR(RANDOM() * array_length(v_causes, 1))::INT],
            v_effects[1 + FLOOR(RANDOM() * array_length(v_effects, 1))::INT],
            v_program_impacts[1 + FLOOR(RANDOM() * array_length(v_program_impacts, 1))::INT],
            ROUND((10 + RANDOM() * 80)::NUMERIC, 2), -- Probability 10-90%
            ROUND((5000 + RANDOM() * 495000)::NUMERIC, 2), -- Impact $5K-$500K
            v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
            CURRENT_DATE + INTERVAL '1 day' * (30 + FLOOR(RANDOM() * 60)::INT), -- Review in 30-90 days
            v_user_id
        ) RETURNING risk_id INTO v_risk_id;

        -- 20% chance this risk becomes an approved change (high probability risks)
        IF RANDOM() < 0.2 AND (SELECT probability FROM public.risk_register WHERE risk_id = v_risk_id) > 70 THEN
            -- Select a random WBS item for the approved change (may be different from original risk)
            v_wbs_item_id := NULL;
            IF RANDOM() < 0.8 THEN
                SELECT wbs_library_item_id
                INTO v_wbs_item_id
                FROM public.wbs_library_item
                WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
                AND level >= 2
                ORDER BY RANDOM()
                LIMIT 1;
            END IF;

            INSERT INTO public.approved_changes (
                project_id,
                title,
                description,
                status,
                wbs_library_item_id,
                date_identified,
                date_approved,
                cause,
                effect,
                program_impact,
                potential_impact,
                mitigation_plan,
                date_for_review,
                risk_owner_user_id,
                approved_by_user_id,
                original_risk_id
            ) VALUES (
                v_project_id,
                'Approved: ' || (SELECT title FROM public.risk_register WHERE risk_id = v_risk_id),
                'This change was approved based on the high likelihood risk: ' ||
                (SELECT description FROM public.risk_register WHERE risk_id = v_risk_id),
                v_approved_statuses[1 + FLOOR(RANDOM() * array_length(v_approved_statuses, 1))::INT],
                v_wbs_item_id,
                (SELECT date_identified FROM public.risk_register WHERE risk_id = v_risk_id),
                CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 30)::INT, -- Approved within last 30 days
                (SELECT cause FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT effect FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT program_impact FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT potential_impact FROM public.risk_register WHERE risk_id = v_risk_id) * (1 + RANDOM() * 0.2), -- 0-20% increase
                'Implementation plan: ' || v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
                CURRENT_DATE + INTERVAL '1 day' * (15 + FLOOR(RANDOM() * 45)::INT), -- Review in 15-60 days
                v_user_id,
                v_user_id, -- Same user approved for simplicity
                v_risk_id
            );
        END IF;
    END LOOP;

    -- Generate some standalone approved changes (not linked to risks) - 5-15 additional changes
    FOR idx IN 1..(5 + FLOOR(RANDOM() * 11)::INT) LOOP
        -- Select a random WBS item
        v_wbs_item_id := NULL;
        IF RANDOM() < 0.8 THEN
            SELECT wbs_library_item_id
            INTO v_wbs_item_id
            FROM public.wbs_library_item
            WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
            AND level >= 2
            ORDER BY RANDOM()
            LIMIT 1;
        END IF;

        INSERT INTO public.approved_changes (
            project_id,
            title,
            description,
            status,
            wbs_library_item_id,
            date_identified,
            date_approved,
            cause,
            effect,
            program_impact,
            potential_impact,
            mitigation_plan,
            date_for_review,
            risk_owner_user_id,
            approved_by_user_id,
            original_risk_id
        ) VALUES (
            v_project_id,
            'Change Request: ' || v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT],
            'Standalone approved change for project improvement and risk mitigation. ' ||
            'This change was identified during project execution and approved for implementation.',
            v_approved_statuses[1 + FLOOR(RANDOM() * array_length(v_approved_statuses, 1))::INT],
            v_wbs_item_id,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 60)::INT,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 20)::INT,
            v_causes[1 + FLOOR(RANDOM() * array_length(v_causes, 1))::INT],
            v_effects[1 + FLOOR(RANDOM() * array_length(v_effects, 1))::INT],
            v_program_impacts[1 + FLOOR(RANDOM() * array_length(v_program_impacts, 1))::INT],
            ROUND((10000 + RANDOM() * 290000)::NUMERIC, 2), -- Impact $10K-$300K
            'Implementation plan: ' || v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
            CURRENT_DATE + INTERVAL '1 day' * (15 + FLOOR(RANDOM() * 45)::INT),
            v_user_id,
            v_user_id,
            NULL -- No original risk
        );
    END LOOP;

    -- Return success information including risk and change counts
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Demo budget, risk register, and approved changes data generated successfully',
        'project_id', v_project_id,
        'project_name', v_project_name,
        'snapshots', jsonb_build_object(
            'stage1', snap1_id,
            'stage2', snap2_id,
            'stage3', snap3_id,
            'stage4', snap4_id
        ),
        'risk_count', (SELECT COUNT(*) FROM public.risk_register WHERE project_id = v_project_id),
        'approved_changes_count', (SELECT COUNT(*) FROM public.approved_changes WHERE project_id = v_project_id)
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Return error information
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to generate demo data: ' || SQLERRM
        );
END;
$_$;

ALTER FUNCTION "public"."generate_demo_project_data" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."get_client_members" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "text",
	"membership_id" "uuid",
	"access_via" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare _client_id uuid;
_has_admin_access boolean;
begin -- Get the client_id for the client name
select client_id into _client_id
from public.client
where name = _client_name;
if _client_id is null then raise exception 'Client not found: %',
_client_name;
end if;
select public.current_user_has_entity_role('client', _client_id, 'admin') into _has_admin_access;
if not _has_admin_access then raise exception 'Access denied: must be a client admin';
end if;
return query
select *
from public.profiles_with_client_access(_client_name);
end;
$$;

ALTER FUNCTION "public"."get_client_members" ("_client_name" "text") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") RETURNS TABLE (
	"client_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"client_url" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"org_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"created_by_user_id" "uuid",
	"organization_name" "text",
	"project_count" bigint,
	"is_client_admin" boolean,
	"is_org_admin" boolean
) LANGUAGE "sql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
SELECT c.client_id,
	c.name,
	c.description,
	c.logo_url,
	c.client_url,
	c.internal_url,
	c.internal_url_description,
	c.org_id,
	c.created_at,
	c.updated_at,
	c.created_by_user_id,
	o.name AS organization_name,
	(
		SELECT COUNT(*)
		FROM public.project p
		WHERE p.client_id = c.client_id
	) AS project_count,
	public.current_user_has_entity_role('client', c.client_id, 'admin') AS is_client_admin,
	public.current_user_has_entity_role('organization', c.org_id, 'admin') AS is_org_admin
FROM public.client c
	INNER JOIN public.organization o ON o.org_id = c.org_id
WHERE o.name = org_name_param
ORDER BY c.name;
$$;

ALTER FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") IS 'Get all clients for an organization with project counts and permission info';

CREATE OR REPLACE FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS "public"."membership_role" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare effective_role public.membership_role;
begin -- Check for direct membership first
select role into effective_role
from public.membership
where user_id = user_id_param
	and entity_type = entity_type_param
	and entity_id = entity_id_param;
if found then return effective_role;
end if;
if entity_type_param = 'project' then
select m.role into effective_role
from public.membership m
	join public.project p on p.project_id = entity_id_param
where m.user_id = user_id_param
	and m.entity_type = 'client'
	and m.entity_id = p.client_id;
if found then return effective_role;
end if;
end if;
if entity_type_param = 'project' then
select m.role into effective_role
from public.membership m
	join public.project p on p.project_id = entity_id_param
	join public.client c on p.client_id = c.client_id
where m.user_id = user_id_param
	and m.entity_type = 'organization'
	and m.entity_id = c.org_id;
elsif entity_type_param = 'client' then
select m.role into effective_role
from public.membership m
	join public.client c on c.client_id = entity_id_param
where m.user_id = user_id_param
	and m.entity_type = 'organization'
	and m.entity_id = c.org_id;
end if;
return effective_role;
end;
$$;

ALTER FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Gets the effective role for a user on an entity, considering the hierarchy';

CREATE OR REPLACE FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS TABLE (
	"entity_type" "public"."entity_type",
	"entity_id" "uuid"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin -- Return the entity itself
	return query
select entity_type_param,
	entity_id_param;
if entity_type_param = 'project' then return query
select 'client'::public.entity_type,
	client_id
from public.project
where project_id = entity_id_param;
return query
select 'organization'::public.entity_type,
	c.org_id
from public.project p
	join public.client c on p.client_id = c.client_id
where p.project_id = entity_id_param;
end if;
if entity_type_param = 'client' then return query
select 'organization'::public.entity_type,
	org_id
from public.client
where client_id = entity_id_param;
end if;
return;
end;
$$;

ALTER FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Returns all ancestor entities for a given entity';

CREATE OR REPLACE FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") RETURNS TABLE (
	"org_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$ BEGIN RETURN QUERY
SELECT o.org_id,
	o.name,
	o.description,
	o.logo_url,
	o.created_by_user_id,
	o.created_at,
	o.updated_at
FROM public.organization o
WHERE o.name = org_name_param;
END;
$$;

ALTER FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."handle_new_user" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin
insert into public.profile (user_id, email, full_name)
values (
		new.id,
		new.email,
		new.raw_user_meta_data->>'full_name'
	);
return new;
end;
$$;

ALTER FUNCTION "public"."handle_new_user" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return exists (
		select 1
		from public.membership m
			join lateral public.get_entity_ancestors(entity_type_param, entity_id_param) a on true
		where m.user_id = user_id_param
			and m.entity_type = a.entity_type
			and m.entity_id = a.entity_id
	);
end;
$$;

ALTER FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if a user has access to an entity through direct membership or ancestor entities';

CREATE OR REPLACE FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare user_role public.membership_role;
begin user_role := public.get_effective_role(
	user_id_param,
	entity_type_param,
	entity_id_param
);
if user_role is null then return false;
end if;
if min_role_param = 'viewer' then return true;
elsif min_role_param = 'editor' then return user_role in ('editor', 'admin', 'owner');
elsif min_role_param = 'admin' then return user_role in ('admin', 'owner');
elsif min_role_param = 'owner' then return user_role = 'owner';
end if;
return false;
end;
$$;

ALTER FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) IS 'Checks if a user has a specific role or higher on an entity';

CREATE OR REPLACE FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") RETURNS "jsonb" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_item JSONB;
    v_wbs_code TEXT;
    v_description TEXT;
    v_quantity NUMERIC;
    v_unit TEXT;
    v_material_rate NUMERIC;
    v_factor NUMERIC;
    v_labor_rate NUMERIC;
    v_productivity_per_hour NUMERIC;
    v_unit_rate NUMERIC;
    v_unit_rate_manual_override BOOLEAN;
    v_remarks TEXT;

    v_wbs_library_id UUID;
    v_client_id UUID;
    v_wbs_item_id UUID;
    v_parent_code TEXT;
    v_parent_item_id UUID;
    v_level INTEGER;
    v_in_level_code TEXT;

    v_inserted_count INTEGER := 0;
    v_wbs_created_count INTEGER := 0;
    v_start_time TIMESTAMPTZ;
    v_duration_ms NUMERIC;

    v_wbs_map JSONB := '{}';
BEGIN
    v_start_time := clock_timestamp();

    -- Get project details
    SELECT p.wbs_library_id, p.client_id
    INTO v_wbs_library_id, v_client_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Project not found: %', p_project_id;
    END IF;

    -- Check permissions
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Insufficient permissions to modify project';
    END IF;

    -- All projects should have a valid wbs_library_id (no null handling needed)
    -- For CostX imports, this will be the Custom WBS library (ID 1)

    -- Process each item (rest of the function remains the same as the original)
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Extract values from JSON
        v_wbs_code := v_item->>'code';
        v_description := v_item->>'description';
        v_quantity := (v_item->>'quantity')::NUMERIC;
        v_unit := v_item->>'unit';
        v_material_rate := (v_item->>'material_rate')::NUMERIC;
        v_factor := CASE WHEN v_item->>'factor' IS NOT NULL THEN (v_item->>'factor')::NUMERIC ELSE NULL END;
        v_labor_rate := CASE WHEN v_item->>'labor_rate' IS NOT NULL THEN (v_item->>'labor_rate')::NUMERIC ELSE NULL END;
        v_productivity_per_hour := CASE WHEN v_item->>'productivity_per_hour' IS NOT NULL THEN (v_item->>'productivity_per_hour')::NUMERIC ELSE NULL END;
        v_unit_rate := CASE WHEN v_item->>'unit_rate' IS NOT NULL THEN (v_item->>'unit_rate')::NUMERIC ELSE NULL END;
        v_unit_rate_manual_override := CASE WHEN v_item->>'unit_rate_manual_override' IS NOT NULL THEN (v_item->>'unit_rate_manual_override')::BOOLEAN ELSE FALSE END;
        v_remarks := v_item->>'remarks';

        -- Validate required fields
        IF v_wbs_code IS NULL OR v_wbs_code = '' THEN
            RAISE EXCEPTION 'WBS code is required for all items';
        END IF;

        IF v_description IS NULL OR v_description = '' THEN
            RAISE EXCEPTION 'Description is required for all items';
        END IF;

        -- Check for duplicate codes in this project
        IF EXISTS (
            SELECT 1 FROM public.wbs_library_item wli
            WHERE wli.code = v_wbs_code
            AND (wli.project_id = p_project_id)
        ) THEN
            RAISE EXCEPTION 'duplicate_code' USING DETAIL = format('WBS code %s already exists in this project', v_wbs_code);
        END IF;

        -- Parse WBS code hierarchy
        WITH wbs_parts AS (
            SELECT string_to_array(v_wbs_code, '.') AS parts
        )
        SELECT
            array_length(parts, 1),
            parts[array_length(parts, 1)],
            CASE
                WHEN array_length(parts, 1) > 1
                THEN array_to_string(parts[1:array_length(parts, 1)-1], '.')
                ELSE NULL
            END
        INTO v_level, v_in_level_code, v_parent_code
        FROM wbs_parts;

        -- Find or create parent WBS item if needed
        v_parent_item_id := NULL;
        IF v_parent_code IS NOT NULL THEN
            -- Check if parent exists in our map first
            IF v_wbs_map ? v_parent_code THEN
                v_parent_item_id := (v_wbs_map->>v_parent_code)::UUID;
            ELSE
                -- Look for existing parent
                SELECT wli.wbs_library_item_id
                INTO v_parent_item_id
                FROM public.wbs_library_item wli
                WHERE wli.code = v_parent_code
                AND wli.wbs_library_id = v_wbs_library_id
                AND (wli.project_id = p_project_id OR wli.client_id = v_client_id OR wli.item_type = 'Standard');

                -- Create parent if it doesn't exist
                IF v_parent_item_id IS NULL THEN
                    WITH parent_parts AS (
                        SELECT string_to_array(v_parent_code, '.') AS parts
                    )
                    INSERT INTO public.wbs_library_item (
                        wbs_library_id,
                        level,
                        in_level_code,
                        parent_item_id,
                        code,
                        description,
                        cost_scope,
                        item_type,
                        client_id,
                        project_id
                    )
                    SELECT
                        v_wbs_library_id,
                        v_level - 1,
                        parts[array_length(parts, 1)],
                        NULL, -- We'd need to recursively create grandparents
                        v_parent_code,
                        'Auto-created parent for ' || v_parent_code,
                        NULL,
                        'Custom',
                        v_client_id,
                        p_project_id
                    FROM parent_parts
                    RETURNING wbs_library_item_id INTO v_parent_item_id;

                    v_wbs_created_count := v_wbs_created_count + 1;
                END IF;

                -- Add to map
                v_wbs_map := v_wbs_map || jsonb_build_object(v_parent_code, v_parent_item_id);
            END IF;
        END IF;

        -- Create WBS library item
        INSERT INTO public.wbs_library_item (
            wbs_library_id,
            level,
            in_level_code,
            parent_item_id,
            code,
            description,
            cost_scope,
            item_type,
            client_id,
            project_id
        ) VALUES (
            v_wbs_library_id,
            v_level,
            v_in_level_code,
            v_parent_item_id,
            v_wbs_code,
            v_description,
            v_description, -- Use description as cost_scope for imported items
            'Custom',
            v_client_id,
            p_project_id
        ) RETURNING wbs_library_item_id INTO v_wbs_item_id;

        v_wbs_created_count := v_wbs_created_count + 1;

        -- Add to map
        v_wbs_map := v_wbs_map || jsonb_build_object(v_wbs_code, v_wbs_item_id);

        -- Create budget line item using existing RPC
        PERFORM public.upsert_budget_line_item(
            p_project_id := p_project_id,
            p_wbs_library_item_id := v_wbs_item_id,
            p_quantity := v_quantity,
            p_unit := v_unit,
            p_material_rate := v_material_rate,
            p_labor_rate := v_labor_rate,
            p_productivity_per_hour := v_productivity_per_hour,
            p_unit_rate_manual_override := v_unit_rate_manual_override,
            p_unit_rate := v_unit_rate,
            p_factor := v_factor,
            p_remarks := v_remarks,
            p_cost_certainty := NULL,
            p_design_certainty := NULL,
            p_change_reason := 'Imported from Excel',
            p_budget_line_item_id := NULL
        );

        v_inserted_count := v_inserted_count + 1;
    END LOOP;

    v_duration_ms := EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000;

    RETURN jsonb_build_object(
        'inserted_count', v_inserted_count,
        'wbs_created_count', v_wbs_created_count,
        'duration_ms', v_duration_ms
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Re-raise the exception to rollback the transaction
        RAISE;
END;
$$;

ALTER FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$;

ALTER FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare org_id_var uuid;
begin
select c.org_id into org_id_var
from public.project p
	join public.client c on p.client_id = c.client_id
where p.project_id = project_id_param;
return public.current_user_has_entity_role('organization', org_id_var, 'admin');
end;
$$;

ALTER FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare client_id_var uuid;
org_id_var uuid;
begin -- Check if user has direct owner role on the project
if public.current_user_has_entity_role('project', project_id_param, 'owner') then return true;
end if;
select client_id into client_id_var
from public.project
where project_id = project_id_param;
if client_id_var is not null
and public.current_user_has_entity_role('client', client_id_var, 'admin') then return true;
end if;
select org_id into org_id_var
from public.client
where client_id = client_id_var;
if org_id_var is not null
and public.current_user_has_entity_role('organization', org_id_var, 'admin') then return true;
end if;
return false;
end;
$$;

ALTER FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") IS 'Checks if the current user is a project owner, client admin, or organization admin for the project';

CREATE OR REPLACE FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_checklist_count INTEGER;
    v_completed_count INTEGER;
BEGIN
    -- Count total checklist items and completed items
    -- Only consider the most recent status for each item (where latest = true)
    SELECT
        COUNT(*),
        COUNT(*) FILTER (
            WHERE status = 'Complete'
        )
    INTO
        v_checklist_count,
        v_completed_count
    FROM
        public.gateway_checklist_item gci
    JOIN
        public.gateway_checklist_item_status_log gcisl
        ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
    WHERE
        gci.project_stage_id = p_project_stage_id
        AND gcisl.latest = TRUE;

    -- If there are no checklist items, stage is ready for completion
    IF v_checklist_count = 0 THEN
        RETURN TRUE;
    END IF;

    -- Stage is ready if all checklist items have a status of 'Complete'
    RETURN v_checklist_count = v_completed_count;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return false to prevent stage completion
        RAISE WARNING 'Error checking if stage is ready for completion: %', SQLERRM;
        RETURN FALSE;
END;
$$;

ALTER FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."log_initial_checklist_item_status" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ BEGIN -- Insert a new status log entry for the newly created checklist item
INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete',
		-- Default initial status
		auth.uid(),
		-- Current user who created the item
		now(),
		-- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."log_initial_checklist_item_status" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "text",
	"membership_id" "uuid",
	"access_via" "text"
) LANGUAGE "sql" STABLE SECURITY DEFINER
SET
	"search_path" TO '' AS $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'organization'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization'
	and m.entity_id = c.org_id
where c.name = _client_name
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'client'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client'
	and m.entity_id = c.client_id
where c.name = _client_name
order by created_at desc;
$$;

ALTER FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"access_via" "text",
	"role" "text"
) LANGUAGE "sql" STABLE SECURITY DEFINER
SET
	"search_path" TO '' AS $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'organization'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization'
	and m.entity_id = c.org_id
	join public.project pr on pr.client_id = c.client_id
where c.name = _client_name
	and pr.name = _project_name
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'client'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client'
	and m.entity_id = c.client_id
	join public.project pr on pr.client_id = c.client_id
where c.name = _client_name
	and pr.name = _project_name
union all
/* ── via PROJECT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'project'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.project pr on m.entity_type = 'project'
	and m.entity_id = pr.project_id
	join public.client c on c.client_id = pr.client_id
where c.name = _client_name
	and pr.name = _project_name
order by created_at desc;
$$;

ALTER FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text" DEFAULT 'Reverted to snapshot'::"text"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE v_project_id UUID;
v_item RECORD;
BEGIN -- Get the project_id from the snapshot and stage
SELECT project_id INTO v_project_id
FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
WHERE bs.budget_snapshot_id = p_budget_snapshot_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
END IF;
FOR v_item IN (
	SELECT *
	FROM public.budget_snapshot_line_item
	WHERE budget_snapshot_id = p_budget_snapshot_id
) LOOP -- Use the upsert function for each item
PERFORM public.upsert_budget_line_item(
	v_project_id,
	v_item.wbs_library_item_id,
	v_item.quantity,
	v_item.unit,
	v_item.material_rate,
	v_item.labor_rate,
	v_item.productivity_per_hour,
	v_item.unit_rate_manual_override,
	v_item.unit_rate,
	v_item.factor,
	v_item.remarks,
	v_item.cost_certainty,
	v_item.design_certainty,
	p_revert_reason,
	NULL -- We want to create new records when reverting, not update existing ones
);
END LOOP;
RETURN TRUE;
END;
$$;

ALTER FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."set_gateway_checklist_item_latest" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ BEGIN -- Un-flag the old latest
UPDATE public.gateway_checklist_item_status_log
SET latest = FALSE
WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
	AND latest = TRUE;
UPDATE public.gateway_checklist_item_status_log
SET latest = TRUE
WHERE log_id = (
		SELECT log_id
		FROM public.gateway_checklist_item_status_log
		WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
		ORDER BY valid_at DESC
		LIMIT 1
	);
RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."set_gateway_checklist_item_latest" () OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."update_updated_at_column" () RETURNS "trigger" LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$ begin new.updated_at = timezone('utc', now());
return new;
end;
$$;

ALTER FUNCTION "public"."update_updated_at_column" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."update_updated_at_column" () IS 'Updates the updated_at timestamp when a record is modified';

CREATE OR REPLACE FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text" DEFAULT NULL::"text",
	"p_material_rate" numeric DEFAULT 0,
	"p_labor_rate" numeric DEFAULT NULL::numeric,
	"p_productivity_per_hour" numeric DEFAULT NULL::numeric,
	"p_unit_rate_manual_override" boolean DEFAULT false,
	"p_unit_rate" numeric DEFAULT 0,
	"p_factor" numeric DEFAULT NULL::numeric,
	"p_remarks" "text" DEFAULT NULL::"text",
	"p_cost_certainty" numeric DEFAULT NULL::numeric,
	"p_design_certainty" numeric DEFAULT NULL::numeric,
	"p_change_reason" "text" DEFAULT NULL::"text",
	"p_budget_line_item_id" "uuid" DEFAULT NULL::"uuid"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE v_calculated_cost NUMERIC;
v_cost_to_use NUMERIC;
v_exists BOOLEAN;
v_budget_line_item_id UUID;
BEGIN -- Calculate the cost (always calculate regardless of override)
v_calculated_cost := public.calculate_unit_item_cost(
	p_material_rate,
	p_labor_rate,
	p_productivity_per_hour
);
IF COALESCE(p_unit_rate_manual_override, FALSE) = TRUE THEN -- When manual override is enabled, use the provided value or fall back to calculated
v_cost_to_use := COALESCE(p_unit_rate, v_calculated_cost);
ELSE -- When manual override is disabled or NULL, always use the calculated value
v_cost_to_use := v_calculated_cost;
END IF;
IF p_budget_line_item_id IS NOT NULL THEN -- Check if the budget line item exists
SELECT EXISTS (
		SELECT 1
		FROM public.budget_line_item_current
		WHERE budget_line_item_id = p_budget_line_item_id
	) INTO v_exists;
IF v_exists THEN -- Update existing record by ID (trigger will handle audit logging)
UPDATE public.budget_line_item_current
SET quantity = p_quantity,
	unit = p_unit,
	material_rate = p_material_rate,
	labor_rate = p_labor_rate,
	productivity_per_hour = p_productivity_per_hour,
	unit_rate_manual_override = p_unit_rate_manual_override,
	unit_rate = v_cost_to_use,
	factor = p_factor,
	remarks = p_remarks,
	cost_certainty = p_cost_certainty,
	design_certainty = p_design_certainty,
	updated_at = now()
WHERE budget_line_item_id = p_budget_line_item_id
RETURNING budget_line_item_id INTO v_budget_line_item_id;
RETURN v_budget_line_item_id;
END IF;
END IF;
INSERT INTO public.budget_line_item_current (
		project_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		p_project_id,
		p_wbs_library_item_id,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	)
RETURNING budget_line_item_id INTO v_budget_line_item_id;
RETURN v_budget_line_item_id;
END;
$$;

ALTER FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) OWNER TO "postgres";

SET
	default_tablespace = '';

SET
	default_table_access_method = "heap";

CREATE TABLE IF NOT EXISTS "public"."approved_changes" (
	"approved_change_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"title" "text" NOT NULL,
	"description" "text" NOT NULL,
	"status" "text" DEFAULT 'approved'::"text" NOT NULL,
	"wbs_library_item_id" "uuid",
	"date_identified" "date" DEFAULT CURRENT_DATE NOT NULL,
	"date_approved" "date" DEFAULT CURRENT_DATE NOT NULL,
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"approved_by_user_id" "uuid",
	"original_risk_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	CONSTRAINT "one_owner_type" CHECK (
		(
			(
				(("risk_owner_user_id" IS NOT NULL))::integer + (
					(("risk_owner_name" IS NOT NULL))::integer + (("risk_owner_email" IS NOT NULL))::integer
				)
			) <= 1
		)
	)
);

ALTER TABLE "public"."approved_changes" OWNER TO "postgres";

COMMENT ON TABLE "public"."approved_changes" IS 'Approved changes register for tracking approved project changes that were previously high-likelihood risks';

COMMENT ON COLUMN "public"."approved_changes"."approved_change_id" IS 'Primary key for the approved changes register (UUID)';

COMMENT ON COLUMN "public"."approved_changes"."project_id" IS 'The project this approved change belongs to';

COMMENT ON COLUMN "public"."approved_changes"."title" IS 'Short title for the approved change';

COMMENT ON COLUMN "public"."approved_changes"."description" IS 'Detailed description of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."status" IS 'Current status of the approved change (approved, implemented, closed)';

COMMENT ON COLUMN "public"."approved_changes"."wbs_library_item_id" IS 'Optional link to a specific WBS item';

COMMENT ON COLUMN "public"."approved_changes"."date_identified" IS 'Date the original risk was identified';

COMMENT ON COLUMN "public"."approved_changes"."date_approved" IS 'Date the change was approved';

COMMENT ON COLUMN "public"."approved_changes"."cause" IS 'How the change arose';

COMMENT ON COLUMN "public"."approved_changes"."effect" IS 'Impact of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."program_impact" IS 'Impact on project schedule';

COMMENT ON COLUMN "public"."approved_changes"."potential_impact" IS 'Financial impact of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."mitigation_plan" IS 'Steps to implement or manage the approved change';

COMMENT ON COLUMN "public"."approved_changes"."date_for_review" IS 'When to revisit or reassess this approved change';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_user_id" IS 'User responsible for implementing the approved change';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_name" IS 'Name of external change owner (if not a system user)';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_email" IS 'Email of external change owner (if not a system user)';

COMMENT ON COLUMN "public"."approved_changes"."approved_by_user_id" IS 'User who approved the change';

COMMENT ON COLUMN "public"."approved_changes"."original_risk_id" IS 'Reference to the original risk that was approved (UUID)';

CREATE TABLE IF NOT EXISTS "public"."approved_changes_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"approved_change_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"date_approved" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"approved_by_user_id" "uuid",
	"original_risk_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "approved_changes_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."approved_changes_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."approved_changes_audit" IS 'Audit log of all changes to approved changes entries';

CREATE TABLE IF NOT EXISTS "public"."budget_line_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"budget_line_item_id" "uuid",
	"project_id" "uuid",
	"wbs_library_item_id" "uuid",
	"quantity" numeric(15, 2),
	"unit" "text",
	"material_rate" numeric(15, 2),
	"labor_rate" numeric(15, 2),
	"productivity_per_hour" numeric(15, 2),
	"unit_rate_manual_override" boolean,
	"unit_rate" numeric(15, 2),
	"factor" numeric(15, 2),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "budget_line_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_line_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_audit" IS 'Audit log of all changes to budget line items';

CREATE TABLE IF NOT EXISTS "public"."budget_line_item_current" (
	"budget_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4) NOT NULL,
	"unit" "text",
	"material_rate" numeric(20, 4) NOT NULL,
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4) NOT NULL,
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_line_item_current" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_current" IS 'Current state of project budget line items';

CREATE TABLE IF NOT EXISTS "public"."budget_snapshot" (
	"budget_snapshot_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"freeze_date" timestamp with time zone DEFAULT "now" () NOT NULL,
	"freeze_reason" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot" IS 'Frozen snapshots of project budgets at stage gateways';

CREATE TABLE IF NOT EXISTS "public"."budget_snapshot_line_item" (
	"budget_snapshot_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"budget_snapshot_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4),
	"unit" "text",
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4),
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot_line_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot_line_item" IS 'Line items in a frozen budget snapshot';

CREATE TABLE IF NOT EXISTS "public"."client" (
	"client_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"client_url" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"logo_url" "text",
	"org_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."client" OWNER TO "postgres";

COMMENT ON TABLE "public"."client" IS 'Client contains all the clients of an organization';

CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item" (
	"gateway_checklist_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item" IS 'Checklist items that must be completed before a stage gateway can be signed off';

CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"gateway_checklist_item_id" "uuid",
	"project_stage_id" "uuid",
	"name" "text",
	"description" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "gateway_checklist_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."gateway_checklist_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_audit" IS 'Audit log of all changes to gateway checklist items';

CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_status_log" (
	"log_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"gateway_checklist_item_id" "uuid" NOT NULL,
	"status" "public"."checklist_item_status" DEFAULT 'Incomplete'::"public"."checklist_item_status" NOT NULL,
	"updated_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"valid_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"latest" boolean DEFAULT false NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item_status_log" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_status_log" IS 'Log of status changes for checklist items';

CREATE TABLE IF NOT EXISTS "public"."invite" (
	"invite_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"resource_type" "public"."invite_resource_type" NOT NULL,
	"resource_id" "uuid" NOT NULL,
	"role" "text" NOT NULL,
	"invitee_email" "text" NOT NULL,
	"token_hash" character(64) NOT NULL,
	"status" "public"."invite_status" DEFAULT 'pending'::"public"."invite_status" NOT NULL,
	"inviter_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_by" "uuid",
	"expires_at" timestamp with time zone NOT NULL
);

ALTER TABLE "public"."invite" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."membership" (
	"membership_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"user_id" "uuid" NOT NULL,
	"role" "public"."membership_role" NOT NULL,
	"entity_type" "public"."entity_type" NOT NULL,
	"entity_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."membership" OWNER TO "postgres";

COMMENT ON TABLE "public"."membership" IS 'Polymorphic membership table for organization, client, and project access control';

CREATE TABLE IF NOT EXISTS "public"."organization" (
	"org_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."organization" OWNER TO "postgres";

COMMENT ON TABLE "public"."organization" IS 'Organization that group users and clients together';

CREATE TABLE IF NOT EXISTS "public"."profile" (
	"user_id" "uuid" NOT NULL,
	"email" "text" NOT NULL,
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."profile" OWNER TO "postgres";

COMMENT ON TABLE "public"."profile" IS 'User profile containing personal information and preferences';

CREATE TABLE IF NOT EXISTS "public"."project" (
	"project_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"client_id" "uuid" NOT NULL,
	"wbs_library_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project" OWNER TO "postgres";

COMMENT ON TABLE "public"."project" IS 'Project containing stages, WBS items, and other project-related data';

COMMENT ON COLUMN "public"."project"."wbs_library_id" IS 'WBS library used for this project. For CostX imports, this should be set to the Custom WBS library (ID 1).';

CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info" (
	"project_gateway_stage_info_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"basement_floors" numeric(20, 4),
	"ground_floor" numeric(20, 4),
	"upper_floors" numeric(20, 4),
	"total_gross_internal_floor_area" numeric(20, 4),
	"usable_area" numeric(20, 4),
	"circulation_area" numeric(20, 4),
	"ancillary_areas" numeric(20, 4),
	"internal_divisions" numeric(20, 4),
	"spaces_not_enclosed" numeric(20, 4),
	"total_gross_internal_floor_area_2" numeric(20, 4),
	"internal_cube" numeric(20, 4),
	"area_of_lowest_floor" numeric(20, 4),
	"site_area" numeric(20, 4),
	"number_of_units" numeric(20, 4),
	"nr_of_storeys" numeric(20, 4),
	"nr_of_storeys_primary" numeric(20, 4),
	"nr_of_storeys_secondary" numeric(20, 4),
	"basement_storeys_included_above" numeric(20, 4),
	"average_storey_height" numeric(20, 4),
	"below_ground_floors" numeric(20, 4),
	"ground_floor_height" numeric(20, 4),
	"above_ground_floors" numeric(20, 4),
	"external_vertical_envelope" numeric(20, 4),
	"additional_data" "jsonb",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project_gateway_stage_info" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_gateway_stage_info" IS 'Stores building details for a project''s gateway stage';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."basement_floors" IS 'Basement floors area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."ground_floor" IS 'Ground floor area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."upper_floors" IS 'Upper floors area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."total_gross_internal_floor_area" IS 'Total (Gross Internal Floor Area) in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."usable_area" IS 'Usable Area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."circulation_area" IS 'Circulation Area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."ancillary_areas" IS 'Ancillary Areas in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."internal_divisions" IS 'Internal Divisions in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."spaces_not_enclosed" IS 'Spaces not enclosed in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."total_gross_internal_floor_area_2" IS 'Total (Gross Internal Floor Area) in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."internal_cube" IS 'Internal cube in m³';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."area_of_lowest_floor" IS 'Area of lowest floor in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."site_area" IS 'Site area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."number_of_units" IS 'Number of units in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."nr_of_storeys" IS 'Number of storeys';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."nr_of_storeys_primary" IS 'Number of storeys (Primary) in Nr';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."nr_of_storeys_secondary" IS 'Number of storeys (Secondary) in Nr';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."basement_storeys_included_above" IS 'Basement storeys included above in Nr';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."average_storey_height" IS 'Average storey height';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."below_ground_floors" IS 'Below ground floor(s) in m';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."ground_floor_height" IS 'Ground floor height in m';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."above_ground_floors" IS 'Above ground floor(s) in m';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."external_vertical_envelope" IS 'External vertical envelope in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."additional_data" IS 'Additional data stored as JSON';

CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_gateway_stage_info_id" "uuid",
	"project_stage_id" "uuid",
	"basement_floors" numeric(15, 2),
	"ground_floor" numeric(15, 2),
	"upper_floors" numeric(15, 2),
	"total_gross_internal_floor_area" numeric(15, 2),
	"usable_area" numeric(15, 2),
	"circulation_area" numeric(15, 2),
	"ancillary_areas" numeric(15, 2),
	"internal_divisions" numeric(15, 2),
	"spaces_not_enclosed" numeric(15, 2),
	"total_gross_internal_floor_area_2" numeric(15, 2),
	"internal_cube" numeric(15, 2),
	"area_of_lowest_floor" numeric(15, 2),
	"site_area" numeric(15, 2),
	"number_of_units" numeric(15, 2),
	"nr_of_storeys" numeric(15, 2),
	"nr_of_storeys_primary" numeric(15, 2),
	"nr_of_storeys_secondary" numeric(15, 2),
	"basement_storeys_included_above" numeric(15, 2),
	"average_storey_height" numeric(15, 2),
	"below_ground_floors" numeric(15, 2),
	"ground_floor_height" numeric(15, 2),
	"above_ground_floors" numeric(15, 2),
	"external_vertical_envelope" numeric(15, 2),
	"additional_data" "jsonb",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_gateway_stage_info_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_gateway_stage_info_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_gateway_stage_info_audit" IS 'Audit log of all changes to project gateway stage information';

CREATE TABLE IF NOT EXISTS "public"."project_stage" (
	"project_stage_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"stage_order" integer NOT NULL,
	"stage" integer,
	"gateway_qualitative_scorecard" "jsonb",
	"date_started" timestamp with time zone,
	"date_completed" timestamp with time zone,
	"completion_notes" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project_stage" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_stage" IS 'Project stages tracking progress and completion status';

COMMENT ON COLUMN "public"."project_stage"."date_completed" IS 'When a stage is completed, set this timestamp. Null indicates in-progress.';

CREATE TABLE IF NOT EXISTS "public"."project_stage_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_stage_id" "uuid",
	"project_id" "uuid",
	"name" "text",
	"description" "text",
	"stage_order" integer,
	"stage" integer,
	"gateway_qualitative_scorecard" "jsonb",
	"date_started" timestamp with time zone,
	"date_completed" timestamp with time zone,
	"completion_notes" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_stage_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_stage_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_stage_audit" IS 'Audit log of all changes to project stages';

CREATE TABLE IF NOT EXISTS "public"."risk_register" (
	"risk_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"title" "text" NOT NULL,
	"description" "text" NOT NULL,
	"status" "text" DEFAULT 'identified'::"text" NOT NULL,
	"wbs_library_item_id" "uuid",
	"date_identified" "date" DEFAULT CURRENT_DATE NOT NULL,
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"probability" numeric(5, 2) DEFAULT 50 NOT NULL,
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	CONSTRAINT "one_owner_type" CHECK (
		(
			(
				(("risk_owner_user_id" IS NOT NULL))::integer + (
					(("risk_owner_name" IS NOT NULL))::integer + (("risk_owner_email" IS NOT NULL))::integer
				)
			) <= 1
		)
	)
);

ALTER TABLE "public"."risk_register" OWNER TO "postgres";

COMMENT ON TABLE "public"."risk_register" IS 'Project risk register for tracking and managing project risks';

COMMENT ON COLUMN "public"."risk_register"."risk_id" IS 'Primary key for the risk register (UUID)';

COMMENT ON COLUMN "public"."risk_register"."project_id" IS 'The project this risk belongs to';

COMMENT ON COLUMN "public"."risk_register"."title" IS 'Short title for the risk';

COMMENT ON COLUMN "public"."risk_register"."description" IS 'Detailed description of the risk';

COMMENT ON COLUMN "public"."risk_register"."status" IS 'Current status of the risk (identified, assessed, mitigated, occurred, closed)';

COMMENT ON COLUMN "public"."risk_register"."wbs_library_item_id" IS 'Optional link to a specific WBS item';

COMMENT ON COLUMN "public"."risk_register"."date_identified" IS 'Date the risk was recorded';

COMMENT ON COLUMN "public"."risk_register"."cause" IS 'How the risk might arise';

COMMENT ON COLUMN "public"."risk_register"."effect" IS 'Potential impact of the risk';

COMMENT ON COLUMN "public"."risk_register"."program_impact" IS 'Impact on project schedule';

COMMENT ON COLUMN "public"."risk_register"."probability" IS 'Chance of occurrence (0-100%)';

COMMENT ON COLUMN "public"."risk_register"."potential_impact" IS 'Estimated financial impact';

COMMENT ON COLUMN "public"."risk_register"."mitigation_plan" IS 'Steps to reduce or eliminate the risk';

COMMENT ON COLUMN "public"."risk_register"."date_for_review" IS 'When to revisit or reassess this risk';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_user_id" IS 'User responsible for monitoring/mitigating the risk';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_name" IS 'Name of external risk owner (if not a system user)';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_email" IS 'Email of external risk owner (if not a system user)';

CREATE TABLE IF NOT EXISTS "public"."risk_register_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"risk_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"probability" numeric(5, 2),
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "risk_register_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."risk_register_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."risk_register_audit" IS 'Audit log of all changes to risk register entries';

CREATE TABLE IF NOT EXISTS "public"."wbs_library" (
	"wbs_library_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library" IS 'Work breakdown structure library containing standardized cost lines for projects.';

CREATE TABLE IF NOT EXISTS "public"."wbs_library_item" (
	"wbs_library_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"wbs_library_id" "uuid" NOT NULL,
	"level" integer NOT NULL,
	"in_level_code" "text" NOT NULL,
	"parent_item_id" "uuid",
	"code" "text" NOT NULL,
	"description" "text" NOT NULL,
	"cost_scope" "text",
	"item_type" "public"."wbs_item_type" DEFAULT 'Custom'::"public"."wbs_item_type" NOT NULL,
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item" IS 'Individual items within WBS library forming hierarchical project structures';

COMMENT ON COLUMN "public"."wbs_library_item"."cost_scope" IS 'Detailed cost scope information for this WBS item';

COMMENT ON COLUMN "public"."wbs_library_item"."item_type" IS 'Type of WBS item - Standard (global library) or Custom (client/project specific)';

COMMENT ON COLUMN "public"."wbs_library_item"."client_id" IS 'The client this custom WBS item belongs to (null for standard items)';

COMMENT ON COLUMN "public"."wbs_library_item"."project_id" IS 'The project this custom WBS item belongs to (null means client-wide or standard)';

CREATE TABLE IF NOT EXISTS "public"."wbs_library_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"wbs_library_item_id" "uuid",
	"wbs_library_id" "uuid",
	"level" integer,
	"in_level_code" "text",
	"parent_item_id" "uuid",
	"code" "text",
	"description" "text",
	"cost_scope" "text",
	"item_type" "text",
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "wbs_library_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."wbs_library_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item_audit" IS 'Audit log of all changes to WBS library items';

ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_pkey" PRIMARY KEY ("approved_change_id");

ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_pkey" PRIMARY KEY ("budget_line_item_id");

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_pkey" PRIMARY KEY ("budget_snapshot_line_item_id");

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_pkey" PRIMARY KEY ("budget_snapshot_id");

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_name_org_id_key" UNIQUE ("name", "org_id");

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_pkey" PRIMARY KEY ("client_id");

ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_pkey" PRIMARY KEY ("gateway_checklist_item_id");

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_pkey" PRIMARY KEY ("log_id");

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_invitee_email_resource_type_resource_id_key" UNIQUE ("invitee_email", "resource_type", "resource_id");

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_pkey" PRIMARY KEY ("invite_id");

ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_pkey" PRIMARY KEY ("membership_id");

ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_name_key" UNIQUE ("name");

ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_pkey" PRIMARY KEY ("org_id");

ALTER TABLE ONLY "public"."profile"
ADD CONSTRAINT "profile_pkey" PRIMARY KEY ("user_id");

ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_pkey" PRIMARY KEY ("project_gateway_stage_info_id");

ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_project_stage_id_key" UNIQUE ("project_stage_id");

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_name_client_id_key" UNIQUE ("name", "client_id");

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_pkey" PRIMARY KEY ("project_id");

ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."project_stage"
ADD CONSTRAINT "project_stage_pkey" PRIMARY KEY ("project_stage_id");

ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_pkey" PRIMARY KEY ("risk_id");

ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_pkey" PRIMARY KEY ("wbs_library_item_id");

ALTER TABLE ONLY "public"."wbs_library"
ADD CONSTRAINT "wbs_library_pkey" PRIMARY KEY ("wbs_library_id");

CREATE INDEX "approved_changes_original_risk_idx" ON "public"."approved_changes" USING "btree" ("original_risk_id");

CREATE INDEX "approved_changes_project_idx" ON "public"."approved_changes" USING "btree" ("project_id");

CREATE INDEX "gateway_checklist_item_status_gateway_checklist_item_id_val_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id", "valid_at" DESC);

CREATE UNIQUE INDEX "gateway_checklist_item_status_log_gateway_checklist_item_id_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id")
WHERE
	("latest" = true);

CREATE INDEX "idx_approved_changes_audit_approved_change_id" ON "public"."approved_changes_audit" USING "btree" ("approved_change_id");

CREATE INDEX "idx_approved_changes_audit_changed_at" ON "public"."approved_changes_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_approved_changes_audit_changed_by" ON "public"."approved_changes_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_approved_changes_audit_project_id" ON "public"."approved_changes_audit" USING "btree" ("project_id");

CREATE INDEX "idx_budget_line_item_audit_changed_at" ON "public"."budget_line_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_budget_line_item_audit_changed_by" ON "public"."budget_line_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_budget_line_item_audit_operation_type" ON "public"."budget_line_item_audit" USING "btree" ("operation_type");

CREATE INDEX "idx_budget_line_item_audit_project_id" ON "public"."budget_line_item_audit" USING "btree" ("project_id");

CREATE INDEX "idx_budget_line_item_current_project_id" ON "public"."budget_line_item_current" USING "btree" ("project_id");

CREATE INDEX "idx_budget_line_item_current_wbs_library_item_id" ON "public"."budget_line_item_current" USING "btree" ("wbs_library_item_id");

CREATE INDEX "idx_budget_snapshot_line_item_wbs_library_item_id" ON "public"."budget_snapshot_line_item" USING "btree" ("wbs_library_item_id");

CREATE INDEX "idx_gateway_checklist_item_audit_changed_at" ON "public"."gateway_checklist_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_gateway_checklist_item_audit_changed_by" ON "public"."gateway_checklist_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_gateway_checklist_item_audit_project_stage_id" ON "public"."gateway_checklist_item_audit" USING "btree" ("project_stage_id");

CREATE INDEX "idx_invite_email_status_expires" ON "public"."invite" USING "btree" ("invitee_email", "status", "expires_at");

CREATE INDEX "idx_project_gateway_stage_info_audit_changed_at" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_project_gateway_stage_info_audit_changed_by" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_project_gateway_stage_info_audit_project_stage_id" ON "public"."project_gateway_stage_info_audit" USING "btree" ("project_stage_id");

CREATE INDEX "idx_project_stage_audit_changed_at" ON "public"."project_stage_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_project_stage_audit_changed_by" ON "public"."project_stage_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_project_stage_audit_operation_type" ON "public"."project_stage_audit" USING "btree" ("operation_type");

CREATE INDEX "idx_project_stage_audit_project_id" ON "public"."project_stage_audit" USING "btree" ("project_id");

CREATE INDEX "idx_risk_register_audit_changed_at" ON "public"."risk_register_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_risk_register_audit_changed_by" ON "public"."risk_register_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_risk_register_audit_project_id" ON "public"."risk_register_audit" USING "btree" ("project_id");

CREATE INDEX "idx_risk_register_audit_risk_id" ON "public"."risk_register_audit" USING "btree" ("risk_id");

CREATE INDEX "idx_wbs_library_item_audit_changed_at" ON "public"."wbs_library_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_wbs_library_item_audit_changed_by" ON "public"."wbs_library_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_wbs_library_item_audit_client_id" ON "public"."wbs_library_item_audit" USING "btree" ("client_id");

CREATE INDEX "idx_wbs_library_item_audit_operation_type" ON "public"."wbs_library_item_audit" USING "btree" ("operation_type");

CREATE INDEX "idx_wbs_library_item_audit_project_id" ON "public"."wbs_library_item_audit" USING "btree" ("project_id");

CREATE INDEX "idx_wbs_library_item_audit_wbs_library_item_id" ON "public"."wbs_library_item_audit" USING "btree" ("wbs_library_item_id");

CREATE INDEX "invite_resource_idx" ON "public"."invite" USING "btree" ("resource_type", "resource_id");

CREATE INDEX "invite_token_hash_idx" ON "public"."invite" USING "btree" ("token_hash")
WHERE
	("status" = 'pending'::"public"."invite_status");

CREATE INDEX "membership_entity_idx" ON "public"."membership" USING "btree" ("entity_type", "entity_id");

CREATE UNIQUE INDEX "membership_unique_entity_user" ON "public"."membership" USING "btree" ("entity_type", "entity_id", "user_id");

CREATE INDEX "membership_user_idx" ON "public"."membership" USING "btree" ("user_id");

CREATE INDEX "project_gateway_stage_info_stage_idx" ON "public"."project_gateway_stage_info" USING "btree" ("project_stage_id");

CREATE INDEX "risk_register_project_idx" ON "public"."risk_register" USING "btree" ("project_id");

CREATE INDEX "wbs_library_item_client_idx" ON "public"."wbs_library_item" USING "btree" ("client_id");

CREATE INDEX "wbs_library_item_project_idx" ON "public"."wbs_library_item" USING "btree" ("project_id");

CREATE OR REPLACE TRIGGER "add_creator_as_admin_organization"
AFTER INSERT ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."add_creator_as_admin" ();

CREATE OR REPLACE TRIGGER "audit_approved_changes_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."approved_changes" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_approved_changes_changes" ();

CREATE OR REPLACE TRIGGER "audit_budget_line_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_line_item_changes" ();

CREATE OR REPLACE TRIGGER "audit_gateway_checklist_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_gateway_checklist_item_changes" ();

CREATE OR REPLACE TRIGGER "audit_project_gateway_stage_info_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."project_gateway_stage_info" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_gateway_stage_info_changes" ();

CREATE OR REPLACE TRIGGER "audit_project_stage_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."project_stage" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_stage_changes" ();

CREATE OR REPLACE TRIGGER "audit_risk_register_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."risk_register" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_risk_register_changes" ();

CREATE OR REPLACE TRIGGER "audit_wbs_library_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_wbs_library_item_changes" ();

CREATE OR REPLACE TRIGGER "check_membership_redundancy_trigger" BEFORE INSERT
OR
UPDATE ON "public"."membership" FOR EACH ROW
EXECUTE FUNCTION "public"."check_membership_redundancy" ();

CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_insert"
AFTER INSERT ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."log_initial_checklist_item_status" ();

CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_status_insert"
AFTER INSERT ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."set_gateway_checklist_item_latest" ();

CREATE OR REPLACE TRIGGER "trg_profile_invites"
AFTER INSERT ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."apply_pending_invites" ();

CREATE OR REPLACE TRIGGER "update_approved_changes_updated_at" BEFORE
UPDATE ON "public"."approved_changes" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_risk_register_updated_at" BEFORE
UPDATE ON "public"."risk_register" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot_line_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."client" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."invite" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."membership" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project_gateway_stage_info" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project_stage" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at_blc" BEFORE
UPDATE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_approved_by_user_id_fkey" FOREIGN KEY ("approved_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_original_risk_id_fkey" FOREIGN KEY ("original_risk_id") REFERENCES "public"."risk_register" ("risk_id") ON UPDATE RESTRICT ON DELETE SET NULL;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_risk_owner_user_id_fkey" FOREIGN KEY ("risk_owner_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_budget_snapshot_id_fkey" FOREIGN KEY ("budget_snapshot_id") REFERENCES "public"."budget_snapshot" ("budget_snapshot_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organization" ("org_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_lo_gateway_checklist_item_id_fkey" FOREIGN KEY ("gateway_checklist_item_id") REFERENCES "public"."gateway_checklist_item" ("gateway_checklist_item_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_inviter_id_fkey" FOREIGN KEY ("inviter_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_stage"
ADD CONSTRAINT "project_stage_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_wbs_library_id_fkey" FOREIGN KEY ("wbs_library_id") REFERENCES "public"."wbs_library" ("wbs_library_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_risk_owner_user_id_fkey" FOREIGN KEY ("risk_owner_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_parent_item_id_fkey" FOREIGN KEY ("parent_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_wbs_library_id_fkey" FOREIGN KEY ("wbs_library_id") REFERENCES "public"."wbs_library" ("wbs_library_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

CREATE POLICY "Admins and invitees can read invites" ON "public"."invite" FOR
SELECT
	TO "authenticated" USING (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							(
								"p"."user_id" = (
									SELECT
										"auth"."uid" () AS "uid"
								)
							)
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	);

CREATE POLICY "Admins and the invitee can update invites" ON "public"."invite"
FOR UPDATE
	TO "authenticated" USING (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							("p"."user_id" = "auth"."uid" ())
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	)
WITH
	CHECK (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							(
								"p"."user_id" = (
									SELECT
										"auth"."uid" () AS "uid"
								)
							)
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	);

CREATE POLICY "Admins can delete invites" ON "public"."invite" FOR DELETE TO "authenticated" USING (
	(
		(
			(
				"resource_type" = 'organization'::"public"."invite_resource_type"
			)
			AND "public"."current_user_has_entity_role" (
				'organization'::"public"."entity_type",
				"resource_id",
				'admin'::"public"."membership_role"
			)
		)
		OR (
			(
				"resource_type" = 'client'::"public"."invite_resource_type"
			)
			AND "public"."current_user_has_entity_role" (
				'client'::"public"."entity_type",
				"resource_id",
				'admin'::"public"."membership_role"
			)
		)
		OR (
			(
				"resource_type" = 'project'::"public"."invite_resource_type"
			)
			AND "public"."can_modify_project" ("resource_id")
		)
	)
);

CREATE POLICY "Admins can delete memberships" ON "public"."membership" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		"entity_type",
		"entity_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Admins can manage memberships" ON "public"."membership" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can update memberships" ON "public"."membership"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Client editors and admins can insert projects" ON "public"."project" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'client'::"public"."entity_type",
			"client_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Client/project admins can delete custom WBS library item" ON "public"."wbs_library_item" FOR DELETE TO "authenticated" USING (
	(
		("item_type" = 'Custom'::"public"."wbs_item_type")
		AND (
			(
				("project_id" IS NOT NULL)
				AND "public"."can_modify_project" ("project_id")
			)
			OR "public"."can_modify_client_wbs" ("client_id")
		)
	)
);

CREATE POLICY "Client/project admins can insert custom WBS library item" ON "public"."wbs_library_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	);

CREATE POLICY "Client/project admins can update custom WBS library item" ON "public"."wbs_library_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	)
WITH
	CHECK (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	);

CREATE POLICY "Organization admins can delete clients" ON "public"."client" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'organization'::"public"."entity_type",
		"org_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Organization members can insert a clients" ON "public"."client" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						"public"."membership" "m"
					WHERE
						(
							(
								"m"."entity_type" = 'organization'::"public"."entity_type"
							)
							AND ("m"."entity_id" = "client"."org_id")
							AND ("m"."user_id" = "auth"."uid" ())
						)
				)
			)
			AND ("created_by_user_id" = "auth"."uid" ())
		)
	);

CREATE POLICY "Organization members can update clients" ON "public"."client"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
	);

CREATE POLICY "Project Editors and Owners can delete gateway checklist item" ON "public"."gateway_checklist_item" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project Editors and Owners can delete project stage" ON "public"."project_stage" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project Editors and Owners can insert approved changes" ON "public"."approved_changes" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can insert gateway checklist item" ON "public"."gateway_checklist_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can insert project stage" ON "public"."project_stage" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can insert risks" ON "public"."risk_register" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update approved changes" ON "public"."approved_changes"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update gateway checklist item" ON "public"."gateway_checklist_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can update project stage" ON "public"."project_stage"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can update risks" ON "public"."risk_register"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Owners can delete approved changes" ON "public"."approved_changes" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Project Owners can delete risks" ON "public"."risk_register" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Project Viewers, Editors, and Owners can view project stage" ON "public"."project_stage" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("project_stage"."project_id") AS "can_access_project"
		)
	);

CREATE POLICY "Project editors and owners can update projects" ON "public"."project"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project editors can delete budget line item" ON "public"."budget_line_item_current" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project editors can delete budget snapshot" ON "public"."budget_snapshot" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project editors can delete budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = (
								SELECT
									"budget_snapshot"."project_stage_id"
								FROM
									"public"."budget_snapshot"
								WHERE
									(
										"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
									)
							)
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project editors can insert budget line item" ON "public"."budget_line_item_current" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can insert budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can insert gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget line item" ON "public"."budget_line_item_current"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget snapshot" ON "public"."budget_snapshot"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget snapshot line item" ON "public"."budget_snapshot_line_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project owners can delete projects" ON "public"."project" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Project viewers can insert budget snapshot" ON "public"."budget_snapshot" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project viewers can view budget line item" ON "public"."budget_line_item_current" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("budget_line_item_current"."project_id") AS "can_access_project"
		)
	);

CREATE POLICY "Project viewers can view budget snapshot" ON "public"."budget_snapshot" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "Project viewers can view budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "Project viewers can view gateway checklist item" ON "public"."gateway_checklist_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "Project viewers can view gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "Service role can delete WBS library" ON "public"."wbs_library" FOR DELETE TO "service_role" USING (true);

CREATE POLICY "Service role can delete standard WBS library item" ON "public"."wbs_library_item" FOR DELETE TO "service_role" USING (
	(
		"item_type" = 'Standard'::"public"."wbs_item_type"
	)
);

CREATE POLICY "Service role can insert WBS library" ON "public"."wbs_library" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Service role can insert standard WBS library item" ON "public"."wbs_library_item" FOR INSERT TO "service_role"
WITH
	CHECK (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can update WBS library" ON "public"."wbs_library"
FOR UPDATE
	TO "service_role" USING (true);

CREATE POLICY "Service role can update standard WBS library item" ON "public"."wbs_library_item"
FOR UPDATE
	TO "service_role" USING (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "System can insert WBS library item audit records" ON "public"."wbs_library_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert approved changes audit records" ON "public"."approved_changes_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert budget line item audit records" ON "public"."budget_line_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert gateway checklist item audit records" ON "public"."gateway_checklist_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert project gateway stage info audit records" ON "public"."project_gateway_stage_info_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert project stage audit records" ON "public"."project_stage_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "System can insert risk register audit records" ON "public"."risk_register_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can create organizations" ON "public"."organization" FOR INSERT TO "authenticated"
WITH
	CHECK (("created_by_user_id" = "auth"."uid" ()));

CREATE POLICY "Users can delete gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR DELETE USING (
	(
		EXISTS (
			SELECT
				1
			FROM
				"public"."project_stage" "ps"
			WHERE
				(
					(
						"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
					)
					AND "public"."current_user_has_entity_role" (
						'project'::"public"."entity_type",
						"ps"."project_id",
						'owner'::"public"."membership_role"
					)
				)
		)
	)
);

CREATE POLICY "Users can delete their own organization" ON "public"."organization" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'organization'::"public"."entity_type",
		"org_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Users can insert gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR INSERT
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can insert their own profile" ON "public"."profile" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can update gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info"
FOR UPDATE
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	)
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can update their own organization" ON "public"."organization"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can update their own profile" ON "public"."profile"
FOR UPDATE
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can view WBS library" ON "public"."wbs_library" FOR
SELECT
	TO "authenticated" USING (true);

CREATE POLICY "Users can view approved changes audit for accessible projects" ON "public"."approved_changes_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view approved changes for projects they have access t" ON "public"."approved_changes" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view budget line item audit for accessible projects" ON "public"."budget_line_item_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view clients they have access to" ON "public"."client" FOR
SELECT
	TO "authenticated" USING (
		(
			"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."project" "p"
					WHERE
						(
							("p"."client_id" = "client"."client_id")
							AND "public"."current_user_has_entity_access" (
								'project'::"public"."entity_type",
								"p"."project_id"
							)
						)
				)
			)
		)
	);

CREATE POLICY "Users can view custom WBS library item audit for accessible ite" ON "public"."wbs_library_item_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			("item_type" = 'Custom'::"text")
			AND (
				(
					("client_id" IS NOT NULL)
					AND "public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
				)
				OR (
					("project_id" IS NOT NULL)
					AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
				)
			)
		)
	);

CREATE POLICY "Users can view custom WBS library item they have access to" ON "public"."wbs_library_item" FOR
SELECT
	TO "authenticated" USING (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
				OR (
					("project_id" IS NOT NULL)
					AND "public"."has_entity_access" (
						"auth"."uid" (),
						'project'::"public"."entity_type",
						"project_id"
					)
				)
			)
		)
	);

CREATE POLICY "Users can view gateway checklist item audit for accessible proj" ON "public"."gateway_checklist_item_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "gateway_checklist_item_audit"."project_stage_id"
						)
						AND "public"."can_access_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can view memberships they have access to" ON "public"."membership" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ("entity_type", "entity_id")
	);

CREATE POLICY "Users can view organizations they have access to" ON "public"."organization" FOR
SELECT
	TO "authenticated" USING (
		(
			"public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."client" "c"
					WHERE
						(
							("c"."org_id" = "organization"."org_id")
							AND "public"."current_user_has_entity_access" ('client'::"public"."entity_type", "c"."client_id")
						)
				)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						(
							"public"."project" "p"
							JOIN "public"."client" "c" ON (("p"."client_id" = "c"."client_id"))
						)
					WHERE
						(
							("c"."org_id" = "organization"."org_id")
							AND "public"."current_user_has_entity_access" (
								'project'::"public"."entity_type",
								"p"."project_id"
							)
						)
				)
			)
		)
	);

CREATE POLICY "Users can view project gateway stage info audit for accessible " ON "public"."project_gateway_stage_info_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info_audit"."project_stage_id"
						)
						AND "public"."can_access_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can view project stage audit for accessible projects" ON "public"."project_stage_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view projects they have access to" ON "public"."project" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

CREATE POLICY "Users can view risk register audit for accessible projects" ON "public"."risk_register_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view risks for projects they have access to" ON "public"."risk_register" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "Users can view stage info for projects they can view" ON "public"."project_gateway_stage_info" FOR
SELECT
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_access_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can view standard WBS library item" ON "public"."wbs_library_item" FOR
SELECT
	TO "authenticated" USING (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Users can view standard WBS library item audit records" ON "public"."wbs_library_item_audit" FOR
SELECT
	TO "authenticated" USING (("item_type" = 'Standard'::"text"));

CREATE POLICY "Users can view their own profile" ON "public"."profile" FOR
SELECT
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

ALTER TABLE "public"."approved_changes" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."approved_changes_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_line_item_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_line_item_current" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_snapshot" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_snapshot_line_item" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."client" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."gateway_checklist_item" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."gateway_checklist_item_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."gateway_checklist_item_status_log" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."invite" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."membership" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."organization" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_gateway_stage_info" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_gateway_stage_info_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_stage" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_stage_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."risk_register" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."risk_register_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."wbs_library" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."wbs_library_item" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."wbs_library_item_audit" ENABLE ROW LEVEL SECURITY;

ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";

GRANT USAGE ON SCHEMA "public" TO "postgres";

GRANT USAGE ON SCHEMA "public" TO "anon";

GRANT USAGE ON SCHEMA "public" TO "authenticated";

GRANT USAGE ON SCHEMA "public" TO "service_role";

GRANT ALL ON FUNCTION "public"."accept_invite" ("token_param" character) TO "anon";

GRANT ALL ON FUNCTION "public"."accept_invite" ("token_param" character) TO "authenticated";

GRANT ALL ON FUNCTION "public"."accept_invite" ("token_param" character) TO "service_role";

GRANT ALL ON FUNCTION "public"."add_creator_as_admin" () TO "anon";

GRANT ALL ON FUNCTION "public"."add_creator_as_admin" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."add_creator_as_admin" () TO "service_role";

GRANT ALL ON FUNCTION "public"."apply_pending_invites" () TO "anon";

GRANT ALL ON FUNCTION "public"."apply_pending_invites" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."apply_pending_invites" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_approved_changes_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_approved_changes_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_approved_changes_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_budget_line_item_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_budget_line_item_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_budget_line_item_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_gateway_checklist_item_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_gateway_checklist_item_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_gateway_checklist_item_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_project_gateway_stage_info_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_project_gateway_stage_info_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_project_gateway_stage_info_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_project_stage_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_project_stage_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_project_stage_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_risk_register_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_risk_register_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_risk_register_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."audit_wbs_library_item_changes" () TO "anon";

GRANT ALL ON FUNCTION "public"."audit_wbs_library_item_changes" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."audit_wbs_library_item_changes" () TO "service_role";

GRANT ALL ON FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) TO "anon";

GRANT ALL ON FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) TO "service_role";

GRANT ALL ON FUNCTION "public"."can_access_client" ("client_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."can_access_client" ("client_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."can_access_client" ("client_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."can_access_project" ("project_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."can_access_project" ("project_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."can_access_project" ("project_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."check_membership_redundancy" () TO "anon";

GRANT ALL ON FUNCTION "public"."check_membership_redundancy" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."check_membership_redundancy" () TO "service_role";

GRANT ALL ON FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) TO "anon";

GRANT ALL ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) TO "anon";

GRANT ALL ON FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) TO "anon";

GRANT ALL ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "anon";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."generate_demo_project_data" () TO "anon";

GRANT ALL ON FUNCTION "public"."generate_demo_project_data" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."generate_demo_project_data" () TO "service_role";

GRANT ALL ON FUNCTION "public"."get_client_members" ("_client_name" "text") TO "anon";

GRANT ALL ON FUNCTION "public"."get_client_members" ("_client_name" "text") TO "authenticated";

GRANT ALL ON FUNCTION "public"."get_client_members" ("_client_name" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") TO "anon";

GRANT ALL ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") TO "authenticated";

GRANT ALL ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") TO "anon";

GRANT ALL ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") TO "authenticated";

GRANT ALL ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."handle_new_user" () TO "anon";

GRANT ALL ON FUNCTION "public"."handle_new_user" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."handle_new_user" () TO "service_role";

GRANT ALL ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "anon";

GRANT ALL ON FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") TO "anon";

GRANT ALL ON FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") TO "authenticated";

GRANT ALL ON FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") TO "service_role";

GRANT ALL ON FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") TO "anon";

GRANT ALL ON FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") TO "authenticated";

GRANT ALL ON FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") TO "service_role";

GRANT ALL ON FUNCTION "public"."log_initial_checklist_item_status" () TO "anon";

GRANT ALL ON FUNCTION "public"."log_initial_checklist_item_status" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."log_initial_checklist_item_status" () TO "service_role";

GRANT ALL ON FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") TO "anon";

GRANT ALL ON FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") TO "authenticated";

GRANT ALL ON FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") TO "anon";

GRANT ALL ON FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") TO "authenticated";

GRANT ALL ON FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) TO "anon";

GRANT ALL ON FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) TO "service_role";

GRANT ALL ON FUNCTION "public"."set_gateway_checklist_item_latest" () TO "anon";

GRANT ALL ON FUNCTION "public"."set_gateway_checklist_item_latest" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."set_gateway_checklist_item_latest" () TO "service_role";

GRANT ALL ON FUNCTION "public"."update_updated_at_column" () TO "anon";

GRANT ALL ON FUNCTION "public"."update_updated_at_column" () TO "authenticated";

GRANT ALL ON FUNCTION "public"."update_updated_at_column" () TO "service_role";

GRANT ALL ON FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) TO "anon";

GRANT ALL ON FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) TO "authenticated";

GRANT ALL ON FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) TO "service_role";

GRANT ALL ON TABLE "public"."approved_changes" TO "anon";

GRANT ALL ON TABLE "public"."approved_changes" TO "authenticated";

GRANT ALL ON TABLE "public"."approved_changes" TO "service_role";

GRANT ALL ON TABLE "public"."approved_changes_audit" TO "anon";

GRANT ALL ON TABLE "public"."approved_changes_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."approved_changes_audit" TO "service_role";

GRANT ALL ON TABLE "public"."budget_line_item_audit" TO "anon";

GRANT ALL ON TABLE "public"."budget_line_item_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."budget_line_item_audit" TO "service_role";

GRANT ALL ON TABLE "public"."budget_line_item_current" TO "anon";

GRANT ALL ON TABLE "public"."budget_line_item_current" TO "authenticated";

GRANT ALL ON TABLE "public"."budget_line_item_current" TO "service_role";

GRANT ALL ON TABLE "public"."budget_snapshot" TO "anon";

GRANT ALL ON TABLE "public"."budget_snapshot" TO "authenticated";

GRANT ALL ON TABLE "public"."budget_snapshot" TO "service_role";

GRANT ALL ON TABLE "public"."budget_snapshot_line_item" TO "anon";

GRANT ALL ON TABLE "public"."budget_snapshot_line_item" TO "authenticated";

GRANT ALL ON TABLE "public"."budget_snapshot_line_item" TO "service_role";

GRANT ALL ON TABLE "public"."client" TO "anon";

GRANT ALL ON TABLE "public"."client" TO "authenticated";

GRANT ALL ON TABLE "public"."client" TO "service_role";

GRANT ALL ON TABLE "public"."gateway_checklist_item" TO "anon";

GRANT ALL ON TABLE "public"."gateway_checklist_item" TO "authenticated";

GRANT ALL ON TABLE "public"."gateway_checklist_item" TO "service_role";

GRANT ALL ON TABLE "public"."gateway_checklist_item_audit" TO "anon";

GRANT ALL ON TABLE "public"."gateway_checklist_item_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."gateway_checklist_item_audit" TO "service_role";

GRANT ALL ON TABLE "public"."gateway_checklist_item_status_log" TO "anon";

GRANT ALL ON TABLE "public"."gateway_checklist_item_status_log" TO "authenticated";

GRANT ALL ON TABLE "public"."gateway_checklist_item_status_log" TO "service_role";

GRANT ALL ON TABLE "public"."invite" TO "anon";

GRANT ALL ON TABLE "public"."invite" TO "authenticated";

GRANT ALL ON TABLE "public"."invite" TO "service_role";

GRANT ALL ON TABLE "public"."membership" TO "anon";

GRANT ALL ON TABLE "public"."membership" TO "authenticated";

GRANT ALL ON TABLE "public"."membership" TO "service_role";

GRANT ALL ON TABLE "public"."organization" TO "anon";

GRANT ALL ON TABLE "public"."organization" TO "authenticated";

GRANT ALL ON TABLE "public"."organization" TO "service_role";

GRANT ALL ON TABLE "public"."profile" TO "anon";

GRANT ALL ON TABLE "public"."profile" TO "authenticated";

GRANT ALL ON TABLE "public"."profile" TO "service_role";

GRANT ALL ON TABLE "public"."project" TO "anon";

GRANT ALL ON TABLE "public"."project" TO "authenticated";

GRANT ALL ON TABLE "public"."project" TO "service_role";

GRANT ALL ON TABLE "public"."project_gateway_stage_info" TO "anon";

GRANT ALL ON TABLE "public"."project_gateway_stage_info" TO "authenticated";

GRANT ALL ON TABLE "public"."project_gateway_stage_info" TO "service_role";

GRANT ALL ON TABLE "public"."project_gateway_stage_info_audit" TO "anon";

GRANT ALL ON TABLE "public"."project_gateway_stage_info_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."project_gateway_stage_info_audit" TO "service_role";

GRANT ALL ON TABLE "public"."project_stage" TO "anon";

GRANT ALL ON TABLE "public"."project_stage" TO "authenticated";

GRANT ALL ON TABLE "public"."project_stage" TO "service_role";

GRANT ALL ON TABLE "public"."project_stage_audit" TO "anon";

GRANT ALL ON TABLE "public"."project_stage_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."project_stage_audit" TO "service_role";

GRANT ALL ON TABLE "public"."risk_register" TO "anon";

GRANT ALL ON TABLE "public"."risk_register" TO "authenticated";

GRANT ALL ON TABLE "public"."risk_register" TO "service_role";

GRANT ALL ON TABLE "public"."risk_register_audit" TO "anon";

GRANT ALL ON TABLE "public"."risk_register_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."risk_register_audit" TO "service_role";

GRANT ALL ON TABLE "public"."wbs_library" TO "anon";

GRANT ALL ON TABLE "public"."wbs_library" TO "authenticated";

GRANT ALL ON TABLE "public"."wbs_library" TO "service_role";

GRANT ALL ON TABLE "public"."wbs_library_item" TO "anon";

GRANT ALL ON TABLE "public"."wbs_library_item" TO "authenticated";

GRANT ALL ON TABLE "public"."wbs_library_item" TO "service_role";

GRANT ALL ON TABLE "public"."wbs_library_item_audit" TO "anon";

GRANT ALL ON TABLE "public"."wbs_library_item_audit" TO "authenticated";

GRANT ALL ON TABLE "public"."wbs_library_item_audit" TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON SEQUENCES TO "postgres";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON SEQUENCES TO "anon";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON SEQUENCES TO "authenticated";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON SEQUENCES TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON FUNCTIONS TO "postgres";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON FUNCTIONS TO "anon";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON FUNCTIONS TO "authenticated";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON FUNCTIONS TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON TABLES TO "postgres";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON TABLES TO "anon";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON TABLES TO "authenticated";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public"
GRANT ALL ON TABLES TO "service_role";

RESET ALL;
