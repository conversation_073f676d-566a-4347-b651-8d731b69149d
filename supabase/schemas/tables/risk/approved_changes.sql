-- Approved Changes Table Schema
-- Approved changes register for tracking approved project changes that were previously high-likelihood risks
-- Approved Changes table (mirrors risk_register but excludes probability field)
CREATE TABLE IF NOT EXISTS "public"."approved_changes" (
	"approved_change_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"title" "text" NOT NULL,
	"description" "text" NOT NULL,
	"status" "text" DEFAULT 'approved'::"text" NOT NULL,
	"wbs_library_item_id" "uuid",
	"date_identified" "date" DEFAULT CURRENT_DATE NOT NULL,
	"date_approved" "date" DEFAULT CURRENT_DATE NOT NULL,
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"approved_by_user_id" "uuid",
	"original_risk_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	CONSTRAINT "one_owner_type" CHECK (
		(
			(
				(("risk_owner_user_id" IS NOT NULL))::integer + (
					(("risk_owner_name" IS NOT NULL))::integer + (("risk_owner_email" IS NOT NULL))::integer
				)
			) <= 1
		)
	)
);

ALTER TABLE "public"."approved_changes" OWNER TO "postgres";

COMMENT ON TABLE "public"."approved_changes" IS 'Approved changes register for tracking approved project changes that were previously high-likelihood risks';

COMMENT ON COLUMN "public"."approved_changes"."approved_change_id" IS 'Primary key for the approved changes register (UUID)';

COMMENT ON COLUMN "public"."approved_changes"."project_id" IS 'The project this approved change belongs to';

COMMENT ON COLUMN "public"."approved_changes"."title" IS 'Short title for the approved change';

COMMENT ON COLUMN "public"."approved_changes"."description" IS 'Detailed description of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."status" IS 'Current status of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."date_approved" IS 'Date the change was approved';

COMMENT ON COLUMN "public"."approved_changes"."approved_by_user_id" IS 'User who approved the change';

COMMENT ON COLUMN "public"."approved_changes"."original_risk_id" IS 'Link to the original risk that became this approved change';

-- Primary key constraint
ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_pkey" PRIMARY KEY ("approved_change_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_risk_owner_user_id_fkey" FOREIGN KEY ("risk_owner_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_approved_by_user_id_fkey" FOREIGN KEY ("approved_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."approved_changes"
ADD CONSTRAINT "approved_changes_original_risk_id_fkey" FOREIGN KEY ("original_risk_id") REFERENCES "public"."risk_register" ("risk_id") ON UPDATE RESTRICT ON DELETE SET NULL;

-- Indexes for performance
CREATE INDEX "approved_changes_project_id_idx" ON "public"."approved_changes" USING "btree" ("project_id");

CREATE INDEX "approved_changes_original_risk_id_idx" ON "public"."approved_changes" USING "btree" ("original_risk_id");

-- Enable Row Level Security
ALTER TABLE "public"."approved_changes" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."approved_changes" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project Editors and Owners can insert approved changes" ON "public"."approved_changes" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update approved changes" ON "public"."approved_changes"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Owners can delete approved changes" ON "public"."approved_changes" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view approved changes for projects they have access t" ON "public"."approved_changes" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
