-- Risk Register Table Schema
-- Project risk register for tracking and managing project risks
-- Risk Register table
CREATE TABLE IF NOT EXISTS "public"."risk_register" (
	"risk_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"title" "text" NOT NULL,
	"description" "text" NOT NULL,
	"status" "text" DEFAULT 'identified'::"text" NOT NULL,
	"wbs_library_item_id" "uuid",
	"date_identified" "date" DEFAULT CURRENT_DATE NOT NULL,
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"probability" numeric(5, 2) DEFAULT 50 NOT NULL,
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	CONSTRAINT "one_owner_type" CHECK (
		(
			(
				(("risk_owner_user_id" IS NOT NULL))::integer + (
					(("risk_owner_name" IS NOT NULL))::integer + (("risk_owner_email" IS NOT NULL))::integer
				)
			) <= 1
		)
	)
);

ALTER TABLE "public"."risk_register" OWNER TO "postgres";

COMMENT ON TABLE "public"."risk_register" IS 'Project risk register for tracking and managing project risks';

COMMENT ON COLUMN "public"."risk_register"."risk_id" IS 'Primary key for the risk register (UUID)';

COMMENT ON COLUMN "public"."risk_register"."project_id" IS 'The project this risk belongs to';

COMMENT ON COLUMN "public"."risk_register"."title" IS 'Short title for the risk';

COMMENT ON COLUMN "public"."risk_register"."description" IS 'Detailed description of the risk';

COMMENT ON COLUMN "public"."risk_register"."status" IS 'Current status of the risk (identified, assessed, mitigated, closed, etc.)';

COMMENT ON COLUMN "public"."risk_register"."wbs_library_item_id" IS 'Optional link to a specific WBS item';

COMMENT ON COLUMN "public"."risk_register"."date_identified" IS 'Date the risk was recorded';

COMMENT ON COLUMN "public"."risk_register"."cause" IS 'How the risk might arise';

COMMENT ON COLUMN "public"."risk_register"."effect" IS 'Potential impact of the risk';

COMMENT ON COLUMN "public"."risk_register"."program_impact" IS 'Impact on project schedule';

COMMENT ON COLUMN "public"."risk_register"."probability" IS 'Chance of occurrence (0-100%)';

COMMENT ON COLUMN "public"."risk_register"."potential_impact" IS 'Estimated financial impact';

COMMENT ON COLUMN "public"."risk_register"."mitigation_plan" IS 'Steps to reduce or eliminate the risk';

-- Primary key constraint
ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_pkey" PRIMARY KEY ("risk_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register"
ADD CONSTRAINT "risk_register_risk_owner_user_id_fkey" FOREIGN KEY ("risk_owner_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "risk_register_project_id_idx" ON "public"."risk_register" USING "btree" ("project_id");

CREATE INDEX "risk_register_status_idx" ON "public"."risk_register" USING "btree" ("status");

CREATE INDEX "risk_register_wbs_library_item_id_idx" ON "public"."risk_register" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."risk_register" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."risk_register" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project Editors and Owners can insert risks" ON "public"."risk_register" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update risks" ON "public"."risk_register"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Owners can delete risks" ON "public"."risk_register" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view risks for projects they have access to" ON "public"."risk_register" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
