-- Gateway Checklist Item Audit Table Schema
-- Audit log of all changes to gateway checklist items
-- Gateway Checklist Item Audit table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"gateway_checklist_item_id" "uuid",
	"project_stage_id" "uuid",
	"name" "text",
	"description" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "gateway_checklist_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."gateway_checklist_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_audit" IS 'Audit log of all changes to gateway checklist items';

-- Primary key constraint
ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "gateway_checklist_item_audit_changed_at_idx" ON "public"."gateway_checklist_item_audit" USING "btree" ("changed_at");

CREATE INDEX "gateway_checklist_item_audit_gateway_checklist_item_id_idx" ON "public"."gateway_checklist_item_audit" USING "btree" ("gateway_checklist_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."gateway_checklist_item_audit" ENABLE ROW LEVEL SECURITY;

-- Gateway Checklist Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_gateway_checklist_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.gateway_checklist_item_id, OLD.project_stage_id, OLD.name, OLD.description,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_gateway_checklist_item_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_gateway_checklist_item_changes" () IS 'Audit trigger function for gateway_checklist_item table';

-- Audit trigger for gateway_checklist_item table
CREATE OR REPLACE TRIGGER "audit_gateway_checklist_item_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_gateway_checklist_item_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert gateway checklist item audit records" ON "public"."gateway_checklist_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view gateway checklist item audit for accessible projects" ON "public"."gateway_checklist_item_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item_audit"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);
