-- Budget Line Item Audit Table Schema
-- Audit log of all changes to budget line items
-- Budget Line Item Audit table
CREATE TABLE IF NOT EXISTS "public"."budget_line_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"budget_line_item_id" "uuid",
	"project_id" "uuid",
	"wbs_library_item_id" "uuid",
	"quantity" numeric(15, 2),
	"unit" "text",
	"material_rate" numeric(15, 2),
	"labor_rate" numeric(15, 2),
	"productivity_per_hour" numeric(15, 2),
	"unit_rate_manual_override" boolean,
	"unit_rate" numeric(15, 2),
	"factor" numeric(15, 2),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "budget_line_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_line_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_audit" IS 'Audit log of all changes to budget line items';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_line_item_audit_changed_at_idx" ON "public"."budget_line_item_audit" USING "btree" ("changed_at");

CREATE INDEX "budget_line_item_audit_budget_line_item_id_idx" ON "public"."budget_line_item_audit" USING "btree" ("budget_line_item_id");

CREATE INDEX "budget_line_item_audit_project_id_idx" ON "public"."budget_line_item_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_line_item_audit" ENABLE ROW LEVEL SECURITY;

-- Budget Line Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_budget_line_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_line_item_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_budget_line_item_changes" () IS 'Audit trigger function for budget_line_item_current table';

-- Audit trigger for budget_line_item_current table
CREATE OR REPLACE TRIGGER "audit_budget_line_item_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_line_item_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert budget line item audit records" ON "public"."budget_line_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view budget line item audit for accessible projects" ON "public"."budget_line_item_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
