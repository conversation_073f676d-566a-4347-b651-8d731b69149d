-- WBS Library Item Audit Table Schema
-- Audit log of all changes to WBS library items
-- WBS Library Item Audit table
CREATE TABLE IF NOT EXISTS "public"."wbs_library_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"wbs_library_item_id" "uuid",
	"wbs_library_id" "uuid",
	"level" integer,
	"in_level_code" "text",
	"parent_item_id" "uuid",
	"code" "text",
	"description" "text",
	"cost_scope" "text",
	"item_type" "text",
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "wbs_library_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."wbs_library_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item_audit" IS 'Audit log of all changes to WBS library items';

-- Primary key constraint
ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "wbs_library_item_audit_changed_at_idx" ON "public"."wbs_library_item_audit" USING "btree" ("changed_at");

CREATE INDEX "wbs_library_item_audit_wbs_library_item_id_idx" ON "public"."wbs_library_item_audit" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."wbs_library_item_audit" ENABLE ROW LEVEL SECURITY;

-- WBS Library Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_wbs_library_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like seed data or system operations
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.wbs_library_item_id, OLD.wbs_library_id, OLD.level, OLD.in_level_code, OLD.parent_item_id,
            OLD.code, OLD.description, OLD.cost_scope, OLD.item_type, OLD.client_id, OLD.project_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_wbs_library_item_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_wbs_library_item_changes" () IS 'Audit trigger function for wbs_library_item table';

-- Audit trigger for wbs_library_item table
CREATE OR REPLACE TRIGGER "audit_wbs_library_item_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_wbs_library_item_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert wbs library item audit records" ON "public"."wbs_library_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view wbs library item audit for accessible items" ON "public"."wbs_library_item_audit" FOR
SELECT
	TO "authenticated" USING (
		CASE
			WHEN "item_type" = 'standard' THEN true
			WHEN "item_type" = 'custom'
			AND "client_id" IS NOT NULL THEN "public"."can_access_client" ("client_id")
			WHEN "item_type" = 'custom'
			AND "project_id" IS NOT NULL THEN "public"."can_access_project" ("project_id")
			ELSE false
		END
	);
