-- Approved Changes Audit Table Schema
-- Audit log of all changes to approved changes entries
-- Approved Changes Audit table
CREATE TABLE IF NOT EXISTS "public"."approved_changes_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"approved_change_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"date_approved" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"approved_by_user_id" "uuid",
	"original_risk_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "approved_changes_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."approved_changes_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."approved_changes_audit" IS 'Audit log of all changes to approved changes entries';

-- Primary key constraint
ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "approved_changes_audit_changed_at_idx" ON "public"."approved_changes_audit" USING "btree" ("changed_at");

CREATE INDEX "approved_changes_audit_approved_change_id_idx" ON "public"."approved_changes_audit" USING "btree" ("approved_change_id");

CREATE INDEX "approved_changes_audit_project_id_idx" ON "public"."approved_changes_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."approved_changes_audit" ENABLE ROW LEVEL SECURITY;

-- Approved Changes Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_approved_changes_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.approved_change_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.date_approved, OLD.cause, OLD.effect, OLD.program_impact, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.approved_by_user_id, OLD.original_risk_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_approved_changes_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_approved_changes_changes" () IS 'Audit trigger function for approved_changes table';

-- Audit trigger for approved_changes table
CREATE OR REPLACE TRIGGER "audit_approved_changes_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."approved_changes" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_approved_changes_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert approved changes audit records" ON "public"."approved_changes_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view approved changes audit for accessible projects" ON "public"."approved_changes_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
