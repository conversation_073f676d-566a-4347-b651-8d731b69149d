-- Project Table Schema
-- Contains project information and belongs to a client
-- Project table
CREATE TABLE IF NOT EXISTS "public"."project" (
	"project_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"client_id" "uuid" NOT NULL,
	"wbs_library_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project" OWNER TO "postgres";

COMMENT ON TABLE "public"."project" IS 'Project containing stages, WBS items, and other project-related data';

COMMENT ON COLUMN "public"."project"."wbs_library_id" IS 'WBS library used for this project. For CostX imports, this should be set to the Custom WBS library (ID 1).';

-- Primary key constraint
ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_pkey" PRIMARY KEY ("project_id");

-- Unique constraint on name within client
ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_name_client_id_key" UNIQUE ("name", "client_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project"
ADD CONSTRAINT "project_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."project" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger for automatic role assignment
CREATE OR REPLACE TRIGGER "add_creator_as_admin_project"
AFTER INSERT ON "public"."project" FOR EACH ROW
EXECUTE FUNCTION "public"."add_creator_as_admin" ();

-- Row Level Security Policies
CREATE POLICY "Client editors and admins can insert projects" ON "public"."project" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'client'::"public"."entity_type",
			"client_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project editors and owners can update projects" ON "public"."project"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view projects they have access to" ON "public"."project" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);
