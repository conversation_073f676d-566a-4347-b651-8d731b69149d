-- Gateway Checklist Item Table Schema
-- Checklist items that must be completed before a stage gateway can be signed off
-- Gateway Checklist Item table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item" (
	"gateway_checklist_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item" IS 'Checklist items that must be completed before a stage gateway can be signed off';

-- Primary key constraint
ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_pkey" PRIMARY KEY ("gateway_checklist_item_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."gateway_checklist_item" ENABLE ROW LEVEL SECURITY;

-- Function to log initial checklist item status
CREATE OR REPLACE FUNCTION "public"."log_initial_checklist_item_status" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN 
	-- Insert a new status log entry for the newly created checklist item
	INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
	VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete', -- Default initial status
		auth.uid(), -- Current user who created the item
		now(), -- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
	
	RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."log_initial_checklist_item_status" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."log_initial_checklist_item_status" () IS 'Automatically creates initial status log entry when a checklist item is created';

-- Function to check if stage is ready for completion
CREATE OR REPLACE FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	incomplete_count INTEGER;
BEGIN 
	-- Count incomplete checklist items for this stage
	SELECT COUNT(*) INTO incomplete_count
	FROM public.gateway_checklist_item gci
	JOIN public.gateway_checklist_item_status_log gcisl ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
	WHERE gci.project_stage_id = p_project_stage_id
		AND gcisl.latest = TRUE
		AND gcisl.status = 'Incomplete';
	
	-- Stage is ready if there are no incomplete items
	RETURN incomplete_count = 0;
END;
$$;

ALTER FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") IS 'Checks if all checklist items for a stage are complete or deferred';

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger to automatically log initial status when a checklist item is created
CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_insert"
AFTER INSERT ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."log_initial_checklist_item_status" ();

-- Row Level Security Policies
CREATE POLICY "Project editors can insert gateway checklist item" ON "public"."gateway_checklist_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update gateway checklist item" ON "public"."gateway_checklist_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete gateway checklist item" ON "public"."gateway_checklist_item" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view gateway checklist item" ON "public"."gateway_checklist_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);
