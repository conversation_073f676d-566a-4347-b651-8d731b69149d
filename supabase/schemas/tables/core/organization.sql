-- Organization Table Schema
-- Contains organization information and serves as the top-level entity in the hierarchy
-- Organization table
CREATE TABLE IF NOT EXISTS "public"."organization" (
	"org_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."organization" OWNER TO "postgres";

COMMENT ON TABLE "public"."organization" IS 'Organization that group users and clients together';

-- Primary key constraint
ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_pkey" PRIMARY KEY ("org_id");

-- Unique constraint on name
ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_name_key" UNIQUE ("name");

-- Foreign key constraint
ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."organization" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger for automatic role assignment
CREATE OR REPLACE TRIGGER "add_creator_as_admin_organization"
AFTER INSERT ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."add_creator_as_admin" ();

-- Row Level Security Policies
CREATE POLICY "Users can create organizations" ON "public"."organization" FOR INSERT TO "authenticated"
WITH
	CHECK (("created_by_user_id" = "auth"."uid" ()));

CREATE POLICY "Users can update their own organization" ON "public"."organization"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view organizations they have access to" ON "public"."organization" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
	);
