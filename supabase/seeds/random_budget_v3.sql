-- Function to generate comprehensive demo data for development/testing
-- Generates: budget data across 4 stages, risk register entries, and approved changes
CREATE OR REPLACE FUNCTION public.generate_demo_project_data () RETURNS jsonb
SET
	search_path = '' AS $$
DECLARE
    v_project_id UUID := gen_random_uuid();
    v_client_id UUID;
    v_wbs_library_id UUID;
		v_project_name TEXT := 'Test Project ' || now()::text;
    v_user_id UUID := auth.uid();
    stage1_id UUID;
    stage2_id UUID;
    stage3_id UUID;
    stage4_id UUID;
    snap1_id UUID;
    snap2_id UUID;
    snap3_id UUID;
    snap4_id UUID;
    item2 RECORD;
    item3 RECORD;
    item4 RECORD;
    stage_total NUMERIC;
    child_count INT;
    base_share NUMERIC;
    portion NUMERIC;
    remaining NUMERIC;
    idx INT;
    v_existing_project_count INT;
    -- Risk register variables
    v_risk_count INT;
    v_risk_id UUID;
    v_wbs_item_id UUID;
    v_risk_titles TEXT[] := ARRAY[
        'Weather delays during construction',
        'Material cost escalation',
        'Skilled labor shortage',
        'Regulatory approval delays',
        'Site access restrictions',
        'Utility relocation required',
        'Ground conditions worse than expected',
        'Design changes requested by client',
        'Supply chain disruptions',
        'Environmental compliance issues',
        'Contractor insolvency risk',
        'Technology integration challenges',
        'Quality control failures',
        'Safety incident potential',
        'Budget overrun risk',
        'Schedule compression required',
        'Stakeholder approval delays',
        'Third-party coordination issues',
        'Equipment failure risk',
        'Currency exchange fluctuations',
        'Political/regulatory changes',
        'Force majeure events',
        'Resource availability constraints',
        'Technical specification changes',
        'Market demand fluctuations'
    ];
    v_risk_statuses TEXT[] := ARRAY['identified', 'assessed', 'mitigated', 'occurred', 'closed'];
    v_approved_statuses TEXT[] := ARRAY['approved', 'implemented', 'closed'];
    v_causes TEXT[] := ARRAY[
        'External market conditions',
        'Regulatory requirements',
        'Technical constraints',
        'Resource limitations',
        'Client requirements',
        'Environmental factors',
        'Economic conditions',
        'Supplier dependencies',
        'Technology limitations',
        'Stakeholder decisions'
    ];
    v_effects TEXT[] := ARRAY[
        'Schedule delays',
        'Cost increases',
        'Quality impacts',
        'Resource reallocation',
        'Scope changes',
        'Performance degradation',
        'Compliance issues',
        'Stakeholder dissatisfaction',
        'Revenue loss',
        'Reputation damage'
    ];
    v_program_impacts TEXT[] := ARRAY[
        '1-2 week delay',
        '2-4 week delay',
        '1-2 month delay',
        'No schedule impact',
        'Minor schedule adjustment',
        'Significant milestone shift',
        'Critical path impact',
        'Phase completion delay',
        'Project completion delay',
        'Parallel work stream impact'
    ];
    v_mitigation_plans TEXT[] := ARRAY[
        'Implement contingency plan and monitor closely',
        'Engage alternative suppliers and negotiate contracts',
        'Increase resource allocation and accelerate timeline',
        'Establish regular stakeholder communication',
        'Develop technical workaround solutions',
        'Create buffer time in project schedule',
        'Implement quality assurance protocols',
        'Establish risk monitoring dashboard',
        'Negotiate contract amendments',
        'Deploy additional project management resources'
    ];
BEGIN
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to generate demo budget data';
    END IF;

    -- Look up client inserted in data.sql
    SELECT client_id INTO v_client_id FROM public.client WHERE name='Unity' LIMIT 1;

    IF v_client_id IS NULL THEN
        RAISE EXCEPTION 'Unity client not found. Please ensure seed data is loaded.';
    END IF;

    -- Look up WBS library ID for ICMS v3
    SELECT wbs_library_id INTO v_wbs_library_id FROM public.wbs_library WHERE name='ICMS v3' LIMIT 1;

    IF v_wbs_library_id IS NULL THEN
        RAISE EXCEPTION 'ICMS v3 WBS library not found. Please ensure seed data is loaded.';
    END IF;

    -- Create project
    INSERT INTO public.project (project_id, name, description, client_id, wbs_library_id, created_by_user_id)
    VALUES (v_project_id, v_project_name, 'Seeded test project', v_client_id, v_wbs_library_id, v_user_id);

    -- Create four stages
    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 1 - Concept', 1, 1)
    RETURNING project_stage_id INTO stage1_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 2 - Design', 2, 2)
    RETURNING project_stage_id INTO stage2_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 3 - Tender', 3, 3)
    RETURNING project_stage_id INTO stage3_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 4 - Construction', 4, 4)
    RETURNING project_stage_id INTO stage4_id;

    -- Stage 1 budget across level 2 items
    FOR item2 IN
        SELECT wbs_library_item_id FROM public.wbs_library_item WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3') AND level = 2
    LOOP
        portion := ROUND((75000000 + RANDOM() * 50000000)::NUMERIC, 2);
        INSERT INTO public.budget_line_item_current (
            project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override
        ) VALUES (
            v_project_id, item2.wbs_library_item_id, 1, portion, portion, FALSE
        );
    END LOOP;

    -- Snapshot after stage 1
    SELECT public.create_budget_snapshot(stage1_id, 'Stage 1 budget') INTO snap1_id;

    -- Prepare for stage 2
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item2 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap1_id
    LOOP
        stage_total := item2.unit_rate * (1 + (RANDOM() * 0.5 - 0.25));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item2.wbs_library_item_id, 1, ROUND(stage_total::NUMERIC, 2), ROUND(stage_total::NUMERIC, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item3 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND((base_share * (0.75 + RANDOM() * 0.5))::NUMERIC, 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining::NUMERIC, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item3.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage2_id, 'Stage 2 budget') INTO snap2_id;

    -- Stage 3: spread to level 4 with +/-15%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item3 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap2_id
    LOOP
        stage_total := item3.unit_rate * (1 + (RANDOM() * 0.3 - 0.15));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item3.wbs_library_item_id, 1, ROUND(stage_total::NUMERIC, 2), ROUND(stage_total::NUMERIC, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item4 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND((base_share * (0.85 + RANDOM() * 0.3))::NUMERIC, 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining::NUMERIC, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage3_id, 'Stage 3 budget') INTO snap3_id;

    -- Stage 4: adjust level 4 values +/-5%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item4 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap3_id
    LOOP
        portion := ROUND((item4.unit_rate * (1 + (RANDOM() * 0.1 - 0.05)))::NUMERIC, 2);
        INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
        VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
    END LOOP;

    SELECT public.create_budget_snapshot(stage4_id, 'Stage 4 budget') INTO snap4_id;

    -- Clear existing risk register and approved changes data for this project
    DELETE FROM public.approved_changes WHERE project_id = v_project_id;
    DELETE FROM public.risk_register WHERE project_id = v_project_id;

    -- Generate risk register entries (15-50 risks per project)
    v_risk_count := 15 + FLOOR(RANDOM() * 36)::INT;

    FOR idx IN 1..v_risk_count LOOP
        -- Select a random WBS item (about 70% of risks will have WBS linkage)
        v_wbs_item_id := NULL;
        IF RANDOM() < 0.7 THEN
            SELECT wbs_library_item_id
            INTO v_wbs_item_id
            FROM public.wbs_library_item
            WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
            AND level >= 2
            ORDER BY RANDOM()
            LIMIT 1;
        END IF;

        -- Insert risk with realistic data
        INSERT INTO public.risk_register (
            project_id,
            title,
            description,
            status,
            wbs_library_item_id,
            date_identified,
            cause,
            effect,
            program_impact,
            probability,
            potential_impact,
            mitigation_plan,
            date_for_review,
            risk_owner_user_id
        ) VALUES (
            v_project_id,
            v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT],
            'Detailed risk description for ' || v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT] ||
            '. This risk requires careful monitoring and may impact project delivery if not properly managed.',
            v_risk_statuses[1 + FLOOR(RANDOM() * array_length(v_risk_statuses, 1))::INT],
            v_wbs_item_id,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 90)::INT,
            v_causes[1 + FLOOR(RANDOM() * array_length(v_causes, 1))::INT],
            v_effects[1 + FLOOR(RANDOM() * array_length(v_effects, 1))::INT],
            v_program_impacts[1 + FLOOR(RANDOM() * array_length(v_program_impacts, 1))::INT],
            ROUND((10 + RANDOM() * 80)::NUMERIC, 2), -- Probability 10-90%
            ROUND((5000 + RANDOM() * 495000)::NUMERIC, 2), -- Impact $5K-$500K
            v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
            CURRENT_DATE + INTERVAL '1 day' * (30 + FLOOR(RANDOM() * 60)::INT), -- Review in 30-90 days
            v_user_id
        ) RETURNING risk_id INTO v_risk_id;

        -- 20% chance this risk becomes an approved change (high probability risks)
        IF RANDOM() < 0.2 AND (SELECT probability FROM public.risk_register WHERE risk_id = v_risk_id) > 70 THEN
            -- Select a random WBS item for the approved change (may be different from original risk)
            v_wbs_item_id := NULL;
            IF RANDOM() < 0.8 THEN
                SELECT wbs_library_item_id
                INTO v_wbs_item_id
                FROM public.wbs_library_item
                WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
                AND level >= 2
                ORDER BY RANDOM()
                LIMIT 1;
            END IF;

            INSERT INTO public.approved_changes (
                project_id,
                title,
                description,
                status,
                wbs_library_item_id,
                date_identified,
                date_approved,
                cause,
                effect,
                program_impact,
                potential_impact,
                mitigation_plan,
                date_for_review,
                risk_owner_user_id,
                approved_by_user_id,
                original_risk_id
            ) VALUES (
                v_project_id,
                'Approved: ' || (SELECT title FROM public.risk_register WHERE risk_id = v_risk_id),
                'This change was approved based on the high likelihood risk: ' ||
                (SELECT description FROM public.risk_register WHERE risk_id = v_risk_id),
                v_approved_statuses[1 + FLOOR(RANDOM() * array_length(v_approved_statuses, 1))::INT],
                v_wbs_item_id,
                (SELECT date_identified FROM public.risk_register WHERE risk_id = v_risk_id),
                CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 30)::INT, -- Approved within last 30 days
                (SELECT cause FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT effect FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT program_impact FROM public.risk_register WHERE risk_id = v_risk_id),
                (SELECT potential_impact FROM public.risk_register WHERE risk_id = v_risk_id) * (1 + RANDOM() * 0.2), -- 0-20% increase
                'Implementation plan: ' || v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
                CURRENT_DATE + INTERVAL '1 day' * (15 + FLOOR(RANDOM() * 45)::INT), -- Review in 15-60 days
                v_user_id,
                v_user_id, -- Same user approved for simplicity
                v_risk_id
            );
        END IF;
    END LOOP;

    -- Generate some standalone approved changes (not linked to risks) - 5-15 additional changes
    FOR idx IN 1..(5 + FLOOR(RANDOM() * 11)::INT) LOOP
        -- Select a random WBS item
        v_wbs_item_id := NULL;
        IF RANDOM() < 0.8 THEN
            SELECT wbs_library_item_id
            INTO v_wbs_item_id
            FROM public.wbs_library_item
            WHERE wbs_library_id = (SELECT wbs_library_id FROM public.wbs_library WHERE name = 'ICMS v3')
            AND level >= 2
            ORDER BY RANDOM()
            LIMIT 1;
        END IF;

        INSERT INTO public.approved_changes (
            project_id,
            title,
            description,
            status,
            wbs_library_item_id,
            date_identified,
            date_approved,
            cause,
            effect,
            program_impact,
            potential_impact,
            mitigation_plan,
            date_for_review,
            risk_owner_user_id,
            approved_by_user_id,
            original_risk_id
        ) VALUES (
            v_project_id,
            'Change Request: ' || v_risk_titles[1 + FLOOR(RANDOM() * array_length(v_risk_titles, 1))::INT],
            'Standalone approved change for project improvement and risk mitigation. ' ||
            'This change was identified during project execution and approved for implementation.',
            v_approved_statuses[1 + FLOOR(RANDOM() * array_length(v_approved_statuses, 1))::INT],
            v_wbs_item_id,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 60)::INT,
            CURRENT_DATE - INTERVAL '1 day' * FLOOR(RANDOM() * 20)::INT,
            v_causes[1 + FLOOR(RANDOM() * array_length(v_causes, 1))::INT],
            v_effects[1 + FLOOR(RANDOM() * array_length(v_effects, 1))::INT],
            v_program_impacts[1 + FLOOR(RANDOM() * array_length(v_program_impacts, 1))::INT],
            ROUND((10000 + RANDOM() * 290000)::NUMERIC, 2), -- Impact $10K-$300K
            'Implementation plan: ' || v_mitigation_plans[1 + FLOOR(RANDOM() * array_length(v_mitigation_plans, 1))::INT],
            CURRENT_DATE + INTERVAL '1 day' * (15 + FLOOR(RANDOM() * 45)::INT),
            v_user_id,
            v_user_id,
            NULL -- No original risk
        );
    END LOOP;

    -- Return success information including risk and change counts
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Demo budget, risk register, and approved changes data generated successfully',
        'project_id', v_project_id,
        'project_name', v_project_name,
        'snapshots', jsonb_build_object(
            'stage1', snap1_id,
            'stage2', snap2_id,
            'stage3', snap3_id,
            'stage4', snap4_id
        ),
        'risk_count', (SELECT COUNT(*) FROM public.risk_register WHERE project_id = v_project_id),
        'approved_changes_count', (SELECT COUNT(*) FROM public.approved_changes WHERE project_id = v_project_id)
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Return error information
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to generate demo data: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to authenticated users only
GRANT
EXECUTE ON FUNCTION public.generate_demo_project_data TO authenticated;
