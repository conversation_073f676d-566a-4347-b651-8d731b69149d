INSERT INTO
	auth.users (
		instance_id,
		id,
		aud,
		role,
		email,
		encrypted_password,
		email_confirmed_at,
		invited_at,
		confirmation_token,
		confirmation_sent_at,
		recovery_token,
		recovery_sent_at,
		email_change_token_new,
		email_change,
		email_change_sent_at,
		last_sign_in_at,
		raw_app_meta_data,
		raw_user_meta_data,
		is_super_admin,
		created_at,
		updated_at,
		phone,
		phone_confirmed_at,
		phone_change,
		phone_change_token,
		phone_change_sent_at,
		email_change_token_current,
		email_change_confirm_status,
		banned_until,
		reauthentication_token,
		reauthentication_sent_at,
		is_sso_user,
		deleted_at,
		is_anonymous
	)
VALUES
	(
		'00000000-0000-0000-0000-000000000000',
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		'authenticated',
		'authenticated',
		'<EMAIL>',
		'$2a$10$DUHzJiN.jPbfhLUKLaJ3Xer8XSpB/FE/JA8lbGHd.nENOFbrxTzE2',
		'2025-04-25 11:14:59.740487+00',
		NULL,
		'',
		NULL,
		'',
		NULL,
		'',
		'',
		NULL,
		'2025-04-25 11:14:59.744091+00',
		'{"provider": "email", "providers": ["email"]}',
		'{"sub": "e7909c19-9a50-41f1-b1b7-26553bedd0a7", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}',
		NULL,
		'2025-04-25 11:14:59.735963+00',
		'2025-04-25 11:14:59.745383+00',
		NULL,
		NULL,
		'',
		'',
		NULL,
		'',
		0,
		NULL,
		'',
		NULL,
		false,
		NULL,
		false
	);

INSERT INTO
	auth.identities (
		provider_id,
		user_id,
		identity_data,
		provider,
		last_sign_in_at,
		created_at,
		updated_at,
		id
	)
VALUES
	(
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		'{"sub": "e7909c19-9a50-41f1-b1b7-26553bedd0a7", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}',
		'email',
		'2025-04-25 11:14:59.739111+00',
		'2025-04-25 11:14:59.739125+00',
		'2025-04-25 11:14:59.739125+00',
		'044fb91b-1250-4d60-bc61-bc48ec5e1d88'
	);

update public.profile
set
	full_name = 'Test User'
where
	user_id = 'e7909c19-9a50-41f1-b1b7-26553bedd0a7';

INSERT INTO
	public.organization (
		org_id,
		name,
		description,
		logo_url,
		created_by_user_id,
		created_at,
		updated_at
	)
VALUES
	(
		'6d8f7927-c31c-46b0-9dfd-fbc2b530088a',
		'Aurora',
		NULL,
		NULL,
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		'2025-04-25 11:15:10.51426+00',
		'2025-04-25 11:15:10.51426+00'
	);

-- The add_creator_as_admin function now handles redundancy checks
INSERT INTO
	public.client (
		name,
		description,
		internal_url,
		internal_url_description,
		client_url,
		created_by_user_id,
		logo_url,
		org_id,
		created_at,
		updated_at
	)
OVERRIDING SYSTEM VALUE
VALUES
	(
		'Unity',
		NULL,
		NULL,
		NULL,
		NULL,
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		NULL,
		'6d8f7927-c31c-46b0-9dfd-fbc2b530088a',
		'2025-04-25 11:25:39.981704+00',
		'2025-04-25 11:25:39.981704+00'
	);

-- Add test user membership
INSERT INTO
	public.membership (
		user_id,
		role,
		entity_type,
		entity_id,
		created_at,
		updated_at
	)
VALUES
	(
		'e7909c19-9a50-41f1-b1b7-26553bedd0a7',
		'admin',
		'organization',
		'6d8f7927-c31c-46b0-9dfd-fbc2b530088a',
		'2025-04-25 11:15:10.51426+00',
		'2025-04-25 11:15:10.51426+00'
	)
ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;

INSERT INTO
	"public"."wbs_library" ("name", "description")
VALUES
	(
		'Custom WBS',
		'Use this for a client- or project-specific WBS'
	);

INSERT INTO
	"public"."wbs_library" ("name", "description")
VALUES
	('ICMS v3', 'ICMS third edition, November 2021');
