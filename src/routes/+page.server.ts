import type { PageServerLoad } from './$types';

export const load = (async ({ locals }) => {
	// load the most recent three projects
	// TODO: This is relying on RLS to filter out projects the user doesn't have access to
	const projects = await locals.supabase
		.from('project')
		.select('*, client:client_id(name, organization(name))')
		.order('updated_at', { ascending: false })
		.limit(3);

	// Load clients with enhanced data including project counts and permissions
	// First get the basic client data with organization info
	const clientsQuery = await locals.supabase
		.from('client')
		.select(
			`
			client_id,
			name,
			description,
			logo_url,
			client_url,
			internal_url,
			internal_url_description,
			org_id,
			created_at,
			updated_at,
			created_by_user_id,
			organization(name)
		`,
		)
		.order('created_at', { ascending: false })
		.limit(3);

	// Enhance client data with project counts and permissions
	const clients = await Promise.all(
		(clientsQuery.data ?? []).map(async (client) => {
			// Get project count for this client
			const { count: projectCount } = await locals.supabase
				.from('project')
				.select('*', { count: 'exact', head: true })
				.eq('client_id', client.client_id);

			// Check permissions using RPC functions
			const { data: isClientAdmin } = await locals.supabase.rpc('current_user_has_entity_role', {
				entity_type_param: 'client',
				entity_id_param: client.client_id,
				min_role_param: 'admin',
			});

			const { data: isOrgAdmin } = await locals.supabase.rpc('current_user_has_entity_role', {
				entity_type_param: 'organization',
				entity_id_param: client.org_id,
				min_role_param: 'admin',
			});

			return {
				...client,
				projectCount: projectCount ?? 0,
				is_client_admin: isClientAdmin ?? false,
				is_org_admin: isOrgAdmin ?? false,
			};
		}),
	);

	return { projects: projects.data ?? [], clients };
}) satisfies PageServerLoad;
