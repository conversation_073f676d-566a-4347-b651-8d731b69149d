<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Button } from '$lib/components/ui/button';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as RadioGroup from '$lib/components/ui/radio-group';
	import * as Select from '$lib/components/ui/select';
	import { superForm } from 'sveltekit-superforms';
	import type { PageData } from './$types';
	import { toast } from 'svelte-sonner';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { slide } from 'svelte/transition';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import { StandardICMSStages, StandardRibaStages } from '$lib/project_utils';
	import { projectSchema } from '$lib/schemas/project';

	const { data }: { data: PageData } = $props();

	const form = superForm(data.form, {
		validators: zodClient(projectSchema),
		dataType: 'json',
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance } = form;

	function addCustomStage() {
		$formData.custom_stages = [...$formData.custom_stages, { name: '', description: '' }];
	}

	function removeCustomStage(index: number) {
		if ($formData.custom_stages.length > 1) {
			$formData.custom_stages = $formData.custom_stages.filter((_, i) => i !== index);
		}
	}

	// Helper functions for stage checkbox management
	function toggleICMSStage(stageIndex: number, checked: boolean) {
		if (checked) {
			$formData.selected_icms_stages = [...$formData.selected_icms_stages, stageIndex];
		} else {
			$formData.selected_icms_stages = $formData.selected_icms_stages.filter(
				(index) => index !== stageIndex,
			);
		}
	}

	function toggleRIBAStage(stageIndex: number, checked: boolean) {
		if (checked) {
			$formData.selected_riba_stages = [...$formData.selected_riba_stages, stageIndex];
		} else {
			$formData.selected_riba_stages = $formData.selected_riba_stages.filter(
				(index) => index !== stageIndex,
			);
		}
	}

	function isICMSStageSelected(stageIndex: number): boolean {
		return $formData.selected_icms_stages.includes(stageIndex);
	}

	function isRIBAStageSelected(stageIndex: number): boolean {
		return $formData.selected_riba_stages.includes(stageIndex);
	}
</script>

<div class="container mx-auto max-w-2xl py-8">
	<h1>Create a Project</h1>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Project Name -->
				<Form.Field {form} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Project Name</Form.Label>
							<Input
								{...props}
								bind:value={$formData.name}
								required
								placeholder="Enter project name"
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Description -->
				<Form.Field {form} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description (optional)</Form.Label>
							<Textarea
								{...props}
								placeholder="Brief description of the project"
								class="resize-none"
								bind:value={$formData.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- CostX Import Question -->
				<Form.Field {form} name="import_from_costx">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Budget Import</Form.Label>
							<Form.Description class="mb-3">
								Do you want to import a budget from CostX? If yes, you'll be able to upload your
								CostX export file after creating the project.
							</Form.Description>
							<div class="flex items-center space-x-2">
								<Checkbox
									{...props}
									checked={$formData.import_from_costx}
									onCheckedChange={(checked) => {
										$formData.import_from_costx = checked;
										// Set Custom WBS library when switching to CostX import
										if (checked) {
											const customLibrary = data.wbsLibraries.find(
												(lib) => lib.name === 'Custom WBS',
											);
											if (customLibrary) {
												$formData.wbs_library_id = customLibrary.wbs_library_id;
												console.log('Set WBS library to Custom WBS:', customLibrary.wbs_library_id);
											} else {
												console.error(
													'Custom WBS library not found. Available libraries:',
													data.wbsLibraries,
												);
											}
										}
										// Note: When switching back to manual selection, we keep the current value
										// The user will need to select a WBS library from the dropdown
									}}
								/>
								<Form.Label class="cursor-pointer font-normal"
									>Import budget from CostX and use its WBS</Form.Label
								>
							</div>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- WBS Library Selection (conditional) -->
				{#if !$formData.import_from_costx}
					<div transition:slide>
						<Form.Field {form} name="wbs_library_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>WBS Library</Form.Label>
									<Select.Root
										type="single"
										bind:value={$formData.wbs_library_id}
										name={props.name}
									>
										<Select.Trigger class="w-full" {...props}>
											{$formData.wbs_library_id
												? data.wbsLibraries.find(
														(lib) => lib.wbs_library_id === $formData.wbs_library_id,
													)?.name
												: 'Select a WBS library'}
										</Select.Trigger>
										<Select.Content>
											{#each data.wbsLibraries as library (library.wbs_library_id)}
												<Select.Item value={String(library.wbs_library_id)} label={library.name} />
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.Description>
								Select a Work Breakdown Structure library for this project
							</Form.Description>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				{/if}

				<!-- Stage Selection -->
				<Form.Fieldset {form} name="stage_selection_type" class="space-y-3">
					<Form.Legend class="text-base">Project Stages</Form.Legend>
					<Form.Description class="mb-4">
						Choose the type of project stages for this project.
					</Form.Description>
					<RadioGroup.Root
						bind:value={$formData.stage_selection_type}
						class="flex flex-col space-y-1"
						name="stage_selection_type"
					>
						<div class="flex items-center space-y-0 space-x-3">
							<Form.Control>
								{#snippet children({ props })}
									<RadioGroup.Item value="icms" {...props} />
									<Form.Label class="font-normal">Standard ICMS Stages</Form.Label>
								{/snippet}
							</Form.Control>
						</div>
						<div class="flex items-center space-y-0 space-x-3">
							<Form.Control>
								{#snippet children({ props })}
									<RadioGroup.Item value="riba" {...props} />
									<Form.Label class="font-normal">Standard RIBA Stages</Form.Label>
								{/snippet}
							</Form.Control>
						</div>
						<div class="flex items-center space-y-0 space-x-3">
							<Form.Control>
								{#snippet children({ props })}
									<RadioGroup.Item value="custom" {...props} />
									<Form.Label class="font-normal">Custom Stages</Form.Label>
								{/snippet}
							</Form.Control>
						</div>
					</RadioGroup.Root>
					<Form.FieldErrors />
				</Form.Fieldset>

				<!-- ICMS Stages Checkboxes (conditional) -->
				{#if $formData.stage_selection_type === 'icms'}
					<div transition:slide>
						<Form.Fieldset {form} name="selected_icms_stages">
							<Form.Legend class="text-base">Select ICMS Stages</Form.Legend>
							<Form.Description class="mb-4">
								Choose which ICMS stages to include in your project. At least one stage must be
								selected.
							</Form.Description>
							<div class="space-y-3">
								{#each StandardICMSStages as stage, index (index)}
									<Form.Field {form} name={`selected_icms_stages[${index}]`}>
										<Form.Control>
											{#snippet children({ props })}
												<div class="flex items-center space-x-2">
													<Checkbox
														{...props}
														checked={isICMSStageSelected(index)}
														onCheckedChange={(checked) => toggleICMSStage(index, checked)}
													/>
													<Form.Label class="cursor-pointer text-sm font-normal">
														{stage.name}
													</Form.Label>
												</div>
											{/snippet}
										</Form.Control>
									</Form.Field>
								{/each}
							</div>
							<Form.FieldErrors />
						</Form.Fieldset>
					</div>
				{/if}

				<!-- RIBA Stages Checkboxes (conditional) -->
				{#if $formData.stage_selection_type === 'riba'}
					<div transition:slide>
						<Form.Fieldset {form} name="selected_riba_stages">
							<Form.Legend class="text-base">Select RIBA Stages</Form.Legend>
							<Form.Description class="mb-4">
								Choose which RIBA stages to include in your project. At least one stage must be
								selected.
							</Form.Description>
							<div class="space-y-3">
								{#each StandardRibaStages as stage, index (index)}
									<Form.Field {form} name={`selected_riba_stages[${index}]`}>
										<Form.Control>
											{#snippet children({ props })}
												<div class="flex items-center space-x-2">
													<Checkbox
														{...props}
														checked={isRIBAStageSelected(index)}
														onCheckedChange={(checked) => toggleRIBAStage(index, checked)}
													/>
													<Form.Label class="cursor-pointer text-sm font-normal">
														{stage.name}
													</Form.Label>
												</div>
											{/snippet}
										</Form.Control>
									</Form.Field>
								{/each}
							</div>
							<Form.FieldErrors />
						</Form.Fieldset>
					</div>
				{/if}

				<!-- Custom Stages Form (conditional) -->
				{#if $formData.stage_selection_type === 'custom'}
					<div transition:slide>
						<Form.Fieldset {form} name="custom_stages">
							<Form.Legend class="text-base">Custom Stages</Form.Legend>
							<Form.Description class="mb-4">
								Define your custom project stages. Each stage should have a name and optional
								description.
							</Form.Description>
							<div class="space-y-4">
								{#each $formData.custom_stages as stage, index (index)}
									<div class="space-y-3 rounded-lg border p-4">
										<div class="flex items-center justify-between">
											<h4 class="text-sm font-medium">Stage {index + 1}</h4>
											{#if $formData.custom_stages.length > 1}
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onclick={() => removeCustomStage(index)}
												>
													<TrashIcon class="h-4 w-4" />
												</Button>
											{/if}
										</div>
										<div class="space-y-3">
											<Form.Field {form} name={`custom_stages[${index}].name`}>
												<Form.Control>
													{#snippet children({ props })}
														<Form.Label class="text-sm">Stage Name</Form.Label>
														<Input
															{...props}
															bind:value={stage.name}
															placeholder="Enter stage name"
															required
														/>
													{/snippet}
												</Form.Control>
												<Form.FieldErrors />
											</Form.Field>
											<Form.Field {form} name={`custom_stages[${index}].description`}>
												<Form.Control>
													{#snippet children({ props })}
														<Form.Label class="text-sm">Description (optional)</Form.Label>
														<Textarea
															{...props}
															bind:value={stage.description}
															placeholder="Enter stage description"
															class="resize-none"
														/>
													{/snippet}
												</Form.Control>
												<Form.FieldErrors />
											</Form.Field>
										</div>
									</div>
								{/each}
								<Button type="button" variant="outline" onclick={addCustomStage} class="w-full">
									<PlusIcon class="mr-2 h-4 w-4" />
									Add Stage
								</Button>
							</div>
							<Form.FieldErrors />
						</Form.Fieldset>
					</div>
				{/if}

				<div class="pt-4">
					<Form.Button class="w-full">Create Project</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
