<script lang="ts">
	import { page } from '$app/state';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();
</script>

<div class="container">
	<p class="mb-4">
		Editor: {data.canEditProject}
	</p>
	<!-- <pre class="max-w-full text-wrap">{JSON.stringify(data.project, null, 2)}</pre> -->

	<h2 class="text-3xl font-semibold">Project Stages</h2>

	<div class="my-8">
		<a
			class="text-lg text-blue-500 underline"
			href="/org/{page.params
				.org_name}/clients/{data.client_name}/projects/{data.project_name}/budget/import"
			>Import a budget</a
		>
	</div>

	<ol class="my-3 max-w-lg list-none space-y-12 pl-0">
		{#if data.project.project_stage.length === 0}
			<p>No project stages defined.</p>
		{:else}
			{#each data.project.project_stage as stage (stage.project_stage_id)}
				<li>
					<a
						class="text-xl font-bold text-blue-600 underline hover:text-blue-800"
						href="/org/{encodeURIComponent(page.params.org_name)}/clients/{encodeURIComponent(
							data.client_name,
						)}/projects/{encodeURIComponent(data.project_name)}/stage-{encodeURIComponent(
							stage.stage_order,
						)}"
					>
						{stage.stage_order}. {stage.name}
					</a>
					<div class="pl-6">
						<div class="prose max-w-none">
							<pre class="font-sans text-wrap">{stage.description || ''}</pre>
						</div>
						{#if stage.date_started}
							<p>Started {stage.date_started}</p>
						{/if}
						{#if stage.date_completed}
							<p>Completed {stage.date_completed}</p>
						{/if}
					</div>
				</li>
			{/each}
		{/if}
	</ol>
</div>
