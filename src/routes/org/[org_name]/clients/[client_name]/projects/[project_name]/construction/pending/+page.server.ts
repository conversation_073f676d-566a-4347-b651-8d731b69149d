import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { riskFilterSchema, riskItemSchema, type riskStatuses } from '$lib/schemas/risk';

export const load: PageServerLoad = async ({ params, locals, cookies, url, depends }) => {
	depends('project:risks');

	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${params.org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	// Get filter parameters from URL
	const statusFilter = (url.searchParams.get('status') || 'all') as
		| (typeof riskStatuses)[number]
		| 'all';
	const dateFromFilter = url.searchParams.get('date_from') || undefined;
	const dateToFilter = url.searchParams.get('date_to') || undefined;
	const wbsItemFilter = url.searchParams.get('wbs_item') || undefined;

	// Fetch WBS items for the project
	const { data: wbsItems } = await supabase
		.from('wbs_library_item')
		.select('wbs_library_item_id, code, description')
		.eq('project_id', projectData.project_id)
		.order('code', { ascending: true });

	// Build query for high-likelihood risks (>50% probability)
	let riskQuery = supabase
		.from('risk_register')
		.select(
			`
			*,
			wbs_item:wbs_library_item(wbs_library_item_id, code, description),
			risk_owner:profile(full_name, email)
		`,
		)
		.eq('project_id', projectData.project_id)
		.gt('probability', 50) // Filter for high-likelihood risks
		.order('date_identified', { ascending: false });

	// Apply additional filters
	if (statusFilter && statusFilter !== 'all') {
		riskQuery = riskQuery.eq('status', statusFilter);
	} else {
		// Exclude closed risks by default unless specifically filtered for closed
		riskQuery = riskQuery.neq('status', 'closed');
	}

	if (dateFromFilter) {
		riskQuery = riskQuery.gte('date_identified', dateFromFilter);
	}

	if (dateToFilter) {
		riskQuery = riskQuery.lte('date_identified', dateToFilter);
	}

	if (wbsItemFilter) {
		riskQuery = riskQuery.eq('wbs_library_item_id', wbsItemFilter);
	}

	// Execute the query
	const { data: risks, error: risksError } = await riskQuery;

	if (risksError) {
		console.error('Error fetching risks:', risksError);
	}

	// Create the form with the risk item schema
	const form = await superValidate({ project_id: projectData.project_id }, zod(riskItemSchema), {
		errors: false,
	});

	// Create the filter form
	const filterForm = await superValidate(
		{
			status: statusFilter,
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_library_item_id: wbsItemFilter,
		},
		zod(riskFilterSchema),
	);

	// Get project team members for risk owner selection
	const { data: projectMembers } = await supabase.rpc('profiles_with_project_access', {
		_project_name: project_name,
		_client_name: client_name,
	});

	return {
		project: projectData,
		client: projectData.client,
		risks: risks || [],
		wbsItems: wbsItems || [],
		canEditProject: !!canEditProject,
		form,
		filterForm,
		projectMembers: projectMembers || [],
	};
};

export const actions: Actions = {
	// Upsert a risk item
	upsertRisk: async ({ request, locals }) => {
		const { supabase } = locals;

		// Validate the form
		const form = await superValidate(request, zod(riskItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Prepare data for upsert
		const { risk_id, ...riskDataWithoutId } = form.data;

		// Perform upsert
		const { error: upsertError } = form.data.risk_id
			? await supabase
					.from('risk_register')
					.update(riskDataWithoutId)
					.eq('risk_id', form.data.risk_id)
			: await supabase.from('risk_register').insert(riskDataWithoutId);

		if (upsertError) {
			console.error('Error upserting risk:', upsertError);
			return message(form, { type: 'error', text: upsertError.message });
		}

		return message(form, {
			type: 'success',
			text: `Risk ${form.data.risk_id ? 'updated' : 'created'} successfully`,
		});
	},

	// Delete a risk item
	deleteRisk: async ({ request, locals }) => {
		const { supabase } = locals;
		const formData = await request.formData();
		const riskId = formData.get('risk_id')?.toString();
		const projectId = formData.get('project_id')?.toString();

		if (!riskId || !projectId) {
			return fail(400, { message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, { message: 'You do not have permission to delete this risk' });
		}

		// Delete the risk
		const { error: deleteError } = await supabase
			.from('risk_register')
			.delete()
			.eq('risk_id', riskId);

		if (deleteError) {
			console.error('Error deleting risk:', deleteError);
			return fail(500, { message: deleteError.message });
		}

		return { success: true };
	},

	// Apply filters
	applyFilters: async ({ request }) => {
		const form = await superValidate(request, zod(riskFilterSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Return the form data to be used for URL parameters
		return { filterForm: form };
	},

	// Approve a risk (mark as closed and create approved change)
	approve: async ({ request, locals }) => {
		const { supabase } = locals;
		const formData = await request.formData();
		const riskId = formData.get('risk_id')?.toString();
		const projectId = formData.get('project_id')?.toString();

		if (!riskId || !projectId) {
			return fail(400, { message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, { message: 'You do not have permission to approve this risk' });
		}

		// Get the current user
		const {
			data: { user },
		} = await supabase.auth.getUser();
		if (!user) {
			return fail(401, { message: 'User not authenticated' });
		}

		// Fetch the risk data
		const { data: risk, error: fetchError } = await supabase
			.from('risk_register')
			.select('*')
			.eq('risk_id', riskId)
			.single();

		if (fetchError || !risk) {
			console.error('Error fetching risk:', fetchError);
			return fail(500, { message: 'Risk not found' });
		}

		// Start a transaction to update risk and create approved change
		const { error: updateError } = await supabase
			.from('risk_register')
			.update({ status: 'closed' })
			.eq('risk_id', riskId);

		if (updateError) {
			console.error('Error updating risk status:', updateError);
			return fail(500, { message: updateError.message });
		}

		// Create approved change record
		const approvedChangeData = {
			project_id: risk.project_id,
			title: risk.title,
			description: risk.description,
			status: 'approved',
			wbs_library_item_id: risk.wbs_library_item_id,
			date_identified: risk.date_identified,
			date_approved: new Date().toISOString().split('T')[0], // Today's date
			cause: risk.cause,
			effect: risk.effect,
			program_impact: risk.program_impact,
			potential_impact: risk.potential_impact,
			mitigation_plan: risk.mitigation_plan,
			date_for_review: risk.date_for_review,
			risk_owner_user_id: risk.risk_owner_user_id,
			risk_owner_name: risk.risk_owner_name,
			risk_owner_email: risk.risk_owner_email,
			approved_by_user_id: user.id,
			original_risk_id: risk.risk_id,
		};

		const { error: insertError } = await supabase
			.from('approved_changes')
			.insert(approvedChangeData);

		if (insertError) {
			console.error('Error creating approved change:', insertError);
			return fail(500, { message: insertError.message });
		}

		return { success: true };
	},
};
