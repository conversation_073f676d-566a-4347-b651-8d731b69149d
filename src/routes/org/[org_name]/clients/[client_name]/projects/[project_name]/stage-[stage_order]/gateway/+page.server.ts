import { superValidate, message } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { type Actions, fail } from '@sveltejs/kit';
import {
	getGatewayChecklistItems,
	upsertGatewayChecklistItem,
	isStageReadyForCompletion,
	completeProjectStage,
} from '$lib/project_utils';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { Constants } from '$lib/database.types';
import type { Json } from '$lib/database.types';
import {
	gatewayStageInfoSchema,
	qualitativeScorecardSchema,
	stageCompletionSchema,
	stageReadinessSchema,
} from '$lib/schemas/gateway';

// Create a type-safe array of valid checklist item statuses
const validStatuses = Constants.public.Enums.checklist_item_status;

// Base schema for checklist items
// This will be dynamically extended in the load function based on the items
const checklistFormSchema = z.object({
	items: z.array(
		z.object({
			gateway_checklist_item_id: z.string().uuid(),
			status: z.enum(validStatuses),
		}),
	),
});

export async function load({ locals, params, cookies }) {
	const { supabase } = locals;
	const { org_name, client_name, project_name, stage_order } = params;

	// Get project
	const { data: project } = await supabase
		.from('project')
		.select('*, client!inner(name)')
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (!project) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/`,
			{ type: 'error', message: 'Not found' },
			cookies,
		);
	}
	// Get current project stage
	const { data: currentStage, error: currentStageError } = await supabase
		.from('project_stage')
		.select('*')
		.eq('project_id', project.project_id)
		.eq('stage_order', Number(stage_order))
		.order('date_started', { ascending: false })
		.limit(1)
		.maybeSingle();

	if (currentStageError) {
		console.error('Error fetching current stage:', currentStageError);
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/`,
			{ type: 'error', message: 'Could not find that project stage' },
			cookies,
		);
	}

	if (!currentStage) {
		console.error('No active project stage found');
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/`,
			{ type: 'error', message: 'Could not find that project stage' },
			cookies,
		);
	}

	// Get checklist items for the current stage
	let checklistItems: Awaited<ReturnType<typeof getGatewayChecklistItems>> = [];
	try {
		checklistItems = await getGatewayChecklistItems(supabase, currentStage.project_stage_id);
	} catch (error) {
		console.error('Error fetching checklist items:', error);
	}

	// Get gateway stage info for the current stage
	let gatewayStageInfo = null;
	const { data: stageInfoData, error: stageInfoError } = await supabase
		.from('project_gateway_stage_info')
		.select('*')
		.eq('project_stage_id', currentStage.project_stage_id)
		.maybeSingle();

	if (stageInfoError) {
		console.error('Error fetching gateway stage info:', stageInfoError);
	} else {
		gatewayStageInfo = stageInfoData;
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: project.project_id,
	});

	// Prepare initial form data for checklist items
	const checklistFormData = {
		items: checklistItems.map((item) => ({
			gateway_checklist_item_id: item.gateway_checklist_item_id,
			status: item.status,
		})),
	};

	// Prepare initial form data for gateway stage info
	const gatewayStageInfoFormData = currentStage
		? {
				project_stage_id: currentStage.project_stage_id,
				// Floor Areas section
				basement_floors: gatewayStageInfo?.basement_floors || null,
				ground_floor: gatewayStageInfo?.ground_floor || null,
				upper_floors: gatewayStageInfo?.upper_floors || null,
				total_gross_internal_floor_area: gatewayStageInfo?.total_gross_internal_floor_area || null,
				usable_area: gatewayStageInfo?.usable_area || null,
				circulation_area: gatewayStageInfo?.circulation_area || null,
				ancillary_areas: gatewayStageInfo?.ancillary_areas || null,
				internal_divisions: gatewayStageInfo?.internal_divisions || null,
				spaces_not_enclosed: gatewayStageInfo?.spaces_not_enclosed || null,
				total_gross_internal_floor_area_2:
					gatewayStageInfo?.total_gross_internal_floor_area_2 || null,
				internal_cube: gatewayStageInfo?.internal_cube || null,
				area_of_lowest_floor: gatewayStageInfo?.area_of_lowest_floor || null,
				site_area: gatewayStageInfo?.site_area || null,
				number_of_units: gatewayStageInfo?.number_of_units || null,
				// Storeys section
				nr_of_storeys: gatewayStageInfo?.nr_of_storeys || null,
				nr_of_storeys_primary: gatewayStageInfo?.nr_of_storeys_primary || null,
				nr_of_storeys_secondary: gatewayStageInfo?.nr_of_storeys_secondary || null,
				basement_storeys_included_above: gatewayStageInfo?.basement_storeys_included_above || null,
				average_storey_height: gatewayStageInfo?.average_storey_height || null,
				below_ground_floors: gatewayStageInfo?.below_ground_floors || null,
				ground_floor_height: gatewayStageInfo?.ground_floor_height || null,
				above_ground_floors: gatewayStageInfo?.above_ground_floors || null,
				external_vertical_envelope: gatewayStageInfo?.external_vertical_envelope || null,
				// Additional data - convert from JSON object to array of tuples with IDs
				additional_data: gatewayStageInfo?.additional_data
					? Object.entries(
							gatewayStageInfo.additional_data as Record<
								string,
								string | { id: string; name: string; value: string }
							>,
						).map(([key, value]) => {
							// Check if the value is already in the new format (has id, name, value)
							if (
								typeof value === 'object' &&
								value !== null &&
								'id' in value &&
								'name' in value &&
								'value' in value
							) {
								return [value.id, value.name, value.value];
							}
							// Otherwise, use the key as both the ID and name for backward compatibility
							return [key, key, typeof value === 'object' ? JSON.stringify(value) : String(value)];
						})
					: null,
			}
		: {};

	// Get qualitative scorecard data from project_stage
	const qualitativeScorecardData = {
		project_stage_id: currentStage?.project_stage_id || '',
		scorecard: null as (string | number | null)[][] | null,
	};

	if (currentStage) {
		// Initialize with default structure if not present
		if (!currentStage.gateway_qualitative_scorecard) {
			// Default structure: [["Dimension Name", "Criterion 1", "Criterion 2", "Criterion 3"], ["Technical specifications", null, null, null]]
			qualitativeScorecardData.scorecard = [
				['Dimension Name', 'Criterion 1', 'Criterion 2', 'Criterion 3'],
				['Technical specifications', null, null, null],
			];
		} else {
			qualitativeScorecardData.scorecard = currentStage.gateway_qualitative_scorecard as (
				| string
				| number
				| null
			)[][];
		}
	}

	// Prepare forms with initial data
	const checklistForm = await superValidate(checklistFormData, zod(checklistFormSchema));

	// Create a new object with the correct type for additional_data
	const typedFormData = {
		...gatewayStageInfoFormData,
		additional_data: gatewayStageInfoFormData.additional_data as [string, string, string][] | null,
	};

	const gatewayStageInfoForm = await superValidate(typedFormData, zod(gatewayStageInfoSchema));

	const qualitativeScorecardForm = await superValidate(
		qualitativeScorecardData,
		zod(qualitativeScorecardSchema),
	);

	// Prepare stage completion form
	const stageCompletionData = {
		project_stage_id: currentStage.project_stage_id,
		notes: '',
	};
	const stageCompletionForm = await superValidate(stageCompletionData, zod(stageCompletionSchema));

	// Prepare stage readiness check form
	const stageReadinessData = {
		project_stage_id: currentStage?.project_stage_id || '',
	};
	const stageReadinessForm = await superValidate(stageReadinessData, zod(stageReadinessSchema));

	return {
		project,
		currentStage,
		checklistItems,
		gatewayStageInfo,
		canEditProject,
		checklistForm,
		gatewayStageInfoForm,
		qualitativeScorecardForm,
		stageCompletionForm,
		stageReadinessForm,
	};
}

export const actions: Actions = {
	// Update gateway stage info
	updateGatewayStageInfo: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { stage_order } = params;
		const { org_name, client_name, project_name } = requireProject(params, cookies);

		if (!stage_order) {
			return redirect(
				org_name ? `/org/${org_name}/clients` : '/',
				{ type: 'error', message: 'Not found' },
				cookies,
			);
		}

		// Get form data
		const form = await superValidate(request, zod(gatewayStageInfoSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project
		const { data: project } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Get current project stage
		const { data: currentStage, error: currentStageError } = await supabase
			.from('project_stage')
			.select('*')
			.eq('project_id', project.project_id)
			.eq('stage_order', Number(stage_order))
			.order('date_started', { ascending: false })
			.limit(1)
			.maybeSingle();

		if (currentStageError) {
			console.error('Error fetching current stage:', currentStageError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to fetch current project stage' },
			});
		}

		if (!currentStage) {
			return fail(404, { form, message: { type: 'error', text: 'No active project stage found' } });
		}

		// Process additional_data to convert from array of tuples to object for database storage
		let additionalData = null;
		if (form.data.additional_data && Array.isArray(form.data.additional_data)) {
			try {
				// Convert array of tuples [id, name, value] to object with structured values
				additionalData = form.data.additional_data.reduce(
					(acc, [id, name, value]) => {
						if (name && name.trim()) {
							// Store each entry as an object with id, name, and value properties
							// This allows us to preserve the ID for future edits
							acc[name.trim()] = {
								id,
								name: name.trim(),
								value,
							};
						}
						return acc;
					},
					{} as Record<string, { id: string; name: string; value: string }>,
				);
			} catch (error) {
				console.error('Error processing additional_data:', error);
				return fail(400, {
					form,
					message: { type: 'error', text: 'Invalid format in additional data' },
				});
			}
		}

		// Check if gateway stage info already exists for this stage
		const { data: existingInfo } = await supabase
			.from('project_gateway_stage_info')
			.select('project_gateway_stage_info_id')
			.eq('project_stage_id', form.data.project_stage_id)
			.maybeSingle();

		let result;
		if (existingInfo) {
			// Update existing record
			result = await supabase
				.from('project_gateway_stage_info')
				.update({
					// Floor Areas section
					basement_floors: form.data.basement_floors,
					ground_floor: form.data.ground_floor,
					upper_floors: form.data.upper_floors,
					total_gross_internal_floor_area: form.data.total_gross_internal_floor_area,
					usable_area: form.data.usable_area,
					circulation_area: form.data.circulation_area,
					ancillary_areas: form.data.ancillary_areas,
					internal_divisions: form.data.internal_divisions,
					spaces_not_enclosed: form.data.spaces_not_enclosed,
					total_gross_internal_floor_area_2: form.data.total_gross_internal_floor_area_2,
					internal_cube: form.data.internal_cube,
					area_of_lowest_floor: form.data.area_of_lowest_floor,
					site_area: form.data.site_area,
					number_of_units: form.data.number_of_units,
					// Storeys section
					nr_of_storeys: form.data.nr_of_storeys,
					nr_of_storeys_primary: form.data.nr_of_storeys_primary,
					nr_of_storeys_secondary: form.data.nr_of_storeys_secondary,
					basement_storeys_included_above: form.data.basement_storeys_included_above,
					average_storey_height: form.data.average_storey_height,
					below_ground_floors: form.data.below_ground_floors,
					ground_floor_height: form.data.ground_floor_height,
					above_ground_floors: form.data.above_ground_floors,
					external_vertical_envelope: form.data.external_vertical_envelope,
					// Additional data
					additional_data: additionalData,
				})
				.eq('project_gateway_stage_info_id', existingInfo.project_gateway_stage_info_id);
		} else {
			// Insert new record
			result = await supabase.from('project_gateway_stage_info').insert({
				project_stage_id: form.data.project_stage_id,
				// Floor Areas section
				basement_floors: form.data.basement_floors,
				ground_floor: form.data.ground_floor,
				upper_floors: form.data.upper_floors,
				total_gross_internal_floor_area: form.data.total_gross_internal_floor_area,
				usable_area: form.data.usable_area,
				circulation_area: form.data.circulation_area,
				ancillary_areas: form.data.ancillary_areas,
				internal_divisions: form.data.internal_divisions,
				spaces_not_enclosed: form.data.spaces_not_enclosed,
				total_gross_internal_floor_area_2: form.data.total_gross_internal_floor_area_2,
				internal_cube: form.data.internal_cube,
				area_of_lowest_floor: form.data.area_of_lowest_floor,
				site_area: form.data.site_area,
				number_of_units: form.data.number_of_units,
				// Storeys section
				nr_of_storeys: form.data.nr_of_storeys,
				nr_of_storeys_primary: form.data.nr_of_storeys_primary,
				nr_of_storeys_secondary: form.data.nr_of_storeys_secondary,
				basement_storeys_included_above: form.data.basement_storeys_included_above,
				average_storey_height: form.data.average_storey_height,
				below_ground_floors: form.data.below_ground_floors,
				ground_floor_height: form.data.ground_floor_height,
				above_ground_floors: form.data.above_ground_floors,
				external_vertical_envelope: form.data.external_vertical_envelope,
				// Additional data
				additional_data: additionalData,
			});
		}

		if (result.error) {
			console.error('Error updating building details:', result.error);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update building details' },
			});
		}

		// Return success message
		return message(form, {
			type: 'success',
			text: 'Building details updated successfully',
		});
	},

	// Update checklist items
	updateChecklist: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name } = requireProject(params, cookies);

		// Get form data
		const form = await superValidate(request, zod(checklistFormSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: project } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Get current project stage
		const { data: stages } = await supabase
			.from('project_stage')
			.select('*')
			.eq('project_id', project.project_id)
			.order('stage_order', { ascending: true });

		const currentStage = stages?.find((stage) => !stage.date_completed) || stages?.[0];

		if (!currentStage) {
			return fail(404, { form, message: { type: 'error', text: 'No active project stage found' } });
		}

		// Process each checklist item update using the upsertGatewayChecklistItem function
		let hasErrors = false;

		for (const item of form.data.items) {
			// We only need to update the status, not the item properties
			const { data, statusUpdated, error } = await upsertGatewayChecklistItem(
				supabase,
				{
					gateway_checklist_item_id: item.gateway_checklist_item_id,
					name: '', // Not used for status-only updates
					description: null, // Not used for status-only updates
					project_stage_id: currentStage.project_stage_id, // Required by the type
				},
				item.status,
			);

			console.log({ data, statusUpdated, error });

			if (error) {
				console.error('Error updating checklist item status:', error);
				hasErrors = true;
			}
		}

		if (hasErrors) {
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update some checklist items' },
			});
		}

		// Return success message
		return message(form, { type: 'success', text: 'Checklist updated successfully' });
	},

	// Update qualitative scorecard
	updateQualitativeScorecard: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name } = requireProject(params, cookies);

		// Get form data
		const form = await superValidate(request, zod(qualitativeScorecardSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			console.error('Error checking project edit permissions:', canEdit);
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Update the project stage with the new scorecard data
		const { error: updateError } = await supabase
			.from('project_stage')
			.update({
				gateway_qualitative_scorecard: form.data.scorecard as unknown as Json,
			})
			.eq('project_stage_id', form.data.project_stage_id);

		if (updateError) {
			console.error('Error updating qualitative scorecard:', updateError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update qualitative scorecard' },
			});
		}

		// Return success message
		return message(form, { type: 'success', text: 'Qualitative scorecard updated successfully' });
	},

	// Check if stage is ready for completion
	checkStageReadiness: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name } = requireProject(params, cookies);

		// Get form data
		const form = await superValidate(request, zod(stageReadinessSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		try {
			// Check if stage is ready for completion
			const isReady = await isStageReadyForCompletion(supabase, form.data.project_stage_id);

			if (!isReady) {
				return message(form, {
					type: 'error',
					text: 'Cannot complete stage: Not all checklist items are completed',
				});
			}

			// Return success message with data
			return {
				form,
				isReady,
				message: { type: 'success', text: 'Stage is ready for completion' },
			};
		} catch (error) {
			console.error('Error checking stage completion status:', error);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Error checking stage completion status' },
			});
		}
	},

	// Complete stage
	completeStage: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { org_name: org_name, client_name, project_name } = requireProject(params, cookies);

		// Get form data
		const form = await superValidate(request, zod(stageCompletionSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Check if stage is ready for completion first
		const isReady = await isStageReadyForCompletion(supabase, form.data.project_stage_id);

		if (!isReady) {
			return message(form, {
				type: 'error',
				text: 'Cannot complete stage: Not all checklist items are completed',
			});
		}

		// Complete the stage
		await completeProjectStage(supabase, form.data.project_stage_id, form.data.notes);

		// Redirect to the project page after successful completion
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}`,
			{ type: 'success', message: 'Stage completed successfully' },
			cookies,
		);
	},
};
