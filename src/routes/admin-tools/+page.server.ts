import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { dev } from '$app/environment';
import { requireUser } from '$lib/server/auth';

// Simple schema for the demo project data generation form
const demoProjectSchema = z.object({
	confirm: z.boolean().refine((val) => val === true, {
		message: 'You must confirm to generate demo project data',
	}),
});

export const load: PageServerLoad = async ({ cookies }) => {
	// Redirect if not in development mode
	if (!dev) {
		throw new Error('Admin tools are only available in development mode');
	}

	// Check authentication
	await requireUser(cookies);

	// Initialize the form
	const form = await superValidate(zod(demoProjectSchema), { errors: false });

	return {
		form,
	};
};

export const actions: Actions = {
	generateDemoProject: async ({ request, locals }) => {
		console.log('Generating demo project data...');
		// Double-check we're in development mode
		if (!dev) {
			return fail(403, { error: 'Admin tools are only available in development mode' });
		}

		const { supabase } = locals;

		// Validate form data
		const form = await superValidate(request, zod(demoProjectSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Call the RPC function to generate demo project data
		const { data, error } = await supabase.rpc('generate_demo_project_data');
		console.log({ data, error });

		if (error) {
			console.error('Error generating demo project data:', error);
			return message(form, {
				type: 'error',
				text: `Failed to generate demo project data: ${error.message}`,
			});
		}

		// Check if the function returned an error
		// Type assertion since we know the structure of our RPC function return
		const result = data as {
			success: boolean;
			error?: string;
			message?: string;
			project_name?: string;
			risk_count?: number;
			approved_changes_count?: number;
		} | null;
		if (result && !result.success) {
			console.error('Demo project generation failed:', result.error);
			return message(form, {
				type: 'error',
				text: result.message || 'Failed to generate demo project data',
			});
		}

		return message(form, {
			type: 'success',
			text: `Demo project data generated successfully! Project: ${result?.project_name || 'Test Project'} with ${result?.risk_count || 0} risks and ${result?.approved_changes_count || 0} approved changes.`,
		});
		// } catch (err) {
		// 	console.error('Unexpected error:', err);
		// 	return message(form, {
		// 		type: 'error',
		// 		text: 'An unexpected error occurred while generating demo project data',
		// 	});
		// }
	},
};
