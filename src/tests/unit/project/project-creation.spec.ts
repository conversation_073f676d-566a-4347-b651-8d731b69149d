import { describe, it, expect } from 'vitest';
import { projectSchema, stageSelectionTypes } from '$lib/schemas/project';
import { StandardRibaStages, StandardICMSStages } from '$lib/project_utils';

describe('Project Creation Schema Validation', () => {
	describe('Stage Selection Types', () => {
		it('should validate ICMS stage selection with selected stages', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
				selected_icms_stages: [0, 1, 2], // Select first 3 ICMS stages
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate RIBA stage selection with selected stages', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'riba' as const,
				selected_riba_stages: [0, 1], // Select first 2 RIBA stages
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject ICMS stage selection without any selected stages', () => {
			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
				selected_icms_stages: [], // No stages selected
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
			expect(result.error?.issues[0].path).toEqual(['selected_icms_stages']);
		});

		it('should reject RIBA stage selection without any selected stages', () => {
			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'riba' as const,
				selected_riba_stages: [], // No stages selected
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
			expect(result.error?.issues[0].path).toEqual(['selected_riba_stages']);
		});

		it('should validate custom stage selection with stages', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'custom' as const,
				custom_stages: [
					{ name: 'Planning', description: 'Initial planning phase' },
					{ name: 'Design', description: 'Design phase' },
				],
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject custom stage selection without stages', () => {
			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'custom' as const,
				custom_stages: [],
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
			expect(result.error?.issues[0].path).toEqual(['custom_stages']);
		});

		it('should reject invalid stage selection type', () => {
			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'invalid',
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});
	});

	describe('Stage Checkbox Selection Validation', () => {
		it('should validate partial ICMS stage selection', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
				selected_icms_stages: [0, 2, 4], // Select stages 0, 2, and 4
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate partial RIBA stage selection', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'riba' as const,
				selected_riba_stages: [1, 3], // Select stages 1 and 3
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate all ICMS stages selected', () => {
			const allICMSStages = StandardICMSStages.map((_, index) => index);
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
				selected_icms_stages: allICMSStages,
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate all RIBA stages selected', () => {
			const allRIBAStages = StandardRibaStages.map((_, index) => index);
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'riba' as const,
				selected_riba_stages: allRIBAStages,
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should ignore RIBA stages when ICMS is selected', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
				selected_icms_stages: [0, 1],
				selected_riba_stages: [], // This should be ignored
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should ignore ICMS stages when RIBA is selected', () => {
			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'riba' as const,
				selected_riba_stages: [0, 1],
				selected_icms_stages: [], // This should be ignored
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});
	});

	describe('Custom Stages Validation', () => {
		it('should validate custom stages with names', () => {
			const validStages = [
				{ name: 'Stage 1', description: 'First stage' },
				{ name: 'Stage 2', description: null },
				{ name: 'Stage 3' },
			];

			const validData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'custom' as const,
				custom_stages: validStages,
			};

			const result = projectSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject custom stages with empty names', () => {
			const invalidStages = [
				{ name: '', description: 'Empty name stage' },
				{ name: 'Valid Stage', description: 'Valid stage' },
			];

			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'custom' as const,
				custom_stages: invalidStages,
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});
	});

	describe('Required Fields', () => {
		it('should require project name', () => {
			const invalidData = {
				name: '',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const,
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});

		it('should require WBS library ID', () => {
			const invalidData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				stage_selection_type: 'icms' as const,
			};

			const result = projectSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});
	});

	describe('Default Values', () => {
		it('should validate with explicit stage selection type and apply field defaults', () => {
			const minimalData = {
				name: 'Test Project',
				wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
				stage_selection_type: 'icms' as const, // Must explicitly specify for discriminated union
				selected_icms_stages: [0], // Need at least one stage for ICMS
			};

			const result = projectSchema.safeParse(minimalData);
			expect(result.success).toBe(true);
			if (result.success) {
				expect(result.data.stage_selection_type).toBe('icms');
				expect(result.data.import_from_costx).toBe(false); // Default value
				expect(result.data.custom_stages).toEqual([]); // Default value
				expect(result.data.selected_icms_stages).toEqual([0]);
				expect(result.data.selected_riba_stages).toEqual([]); // Default value
			}
		});
	});
});

describe('Standard Stages Data', () => {
	it('should have valid ICMS stages structure', () => {
		expect(StandardICMSStages).toBeDefined();
		expect(Array.isArray(StandardICMSStages)).toBe(true);
		expect(StandardICMSStages.length).toBeGreaterThan(0);

		StandardICMSStages.forEach((stage, index) => {
			expect(stage).toHaveProperty('name');
			expect(stage).toHaveProperty('stage_order');
			expect(typeof stage.name).toBe('string');
			expect(typeof stage.stage_order).toBe('number');
			expect(stage.stage_order).toBe(index);
		});
	});

	it('should have valid RIBA stages structure', () => {
		expect(StandardRibaStages).toBeDefined();
		expect(Array.isArray(StandardRibaStages)).toBe(true);
		expect(StandardRibaStages.length).toBeGreaterThan(0);

		StandardRibaStages.forEach((stage, index) => {
			expect(stage).toHaveProperty('name');
			expect(stage).toHaveProperty('stage_order');
			expect(typeof stage.name).toBe('string');
			expect(typeof stage.stage_order).toBe('number');
			expect(stage.stage_order).toBe(index);
		});
	});

	it('should have different ICMS and RIBA stages', () => {
		expect(StandardICMSStages).not.toEqual(StandardRibaStages);
	});
});

describe('Stage Selection Types', () => {
	it('should export valid stage selection types', () => {
		expect(stageSelectionTypes).toEqual(['icms', 'riba', 'custom']);
	});
});
