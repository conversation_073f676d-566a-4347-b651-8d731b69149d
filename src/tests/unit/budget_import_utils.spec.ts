import { describe, it, expect } from 'vitest';
import {
	matchColumns,
	parseWbsCode,
	buildCategoryPrefix,
	classifyRow,
	applyCategoriesToRows,
	collectIsolatedCategories,
	validateColumnMapping,
	transformToImportData,
	type ColumnMapping,
	type ProcessedRow,
} from '$lib/budget_import_utils';

describe('matchColumns', () => {
	it('should match common column headers', () => {
		const headers = ['Code', 'Description', 'Quantity', 'UOM', 'Rate', 'SubTotal'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
		expect(mapping.subtotal).toBe(5);
	});

	it('should handle case-insensitive matching', () => {
		const headers = ['code', 'DESCRIPTION', 'qty', 'Unit', 'material rate'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
	});

	it('should handle WBS code variations', () => {
		const headers = ['WBS Code', 'WBS.Code', 'Cost Code'];
		const mapping1 = matchColumns([headers[0]]);
		const mapping2 = matchColumns([headers[1]]);
		const mapping3 = matchColumns([headers[2]]);

		expect(mapping1.code).toBe(0);
		expect(mapping2.code).toBe(0);
		expect(mapping3.code).toBe(0);
	});

	it('should not map unknown headers', () => {
		const headers = ['Unknown1', 'Unknown2', 'Random'];
		const mapping = matchColumns(headers);

		expect(Object.values(mapping).every((v) => v === undefined)).toBe(true);
	});
});

describe('parseWbsCode', () => {
	it('should parse single level codes', () => {
		const result = parseWbsCode('1');
		expect(result).toEqual({
			level: 1,
			in_level_code: '1',
			parent_code: null,
		});
	});

	it('should parse multi-level codes', () => {
		const result = parseWbsCode('1.2.3');
		expect(result).toEqual({
			level: 3,
			in_level_code: '3',
			parent_code: '1.2',
		});
	});

	it('should handle complex codes', () => {
		const result = parseWbsCode('A.B.C.D.E');
		expect(result).toEqual({
			level: 5,
			in_level_code: 'E',
			parent_code: 'A.B.C.D',
		});
	});

	it('should throw error for invalid codes', () => {
		expect(() => parseWbsCode('')).toThrow('Invalid WBS code');
		expect(() => parseWbsCode(null as unknown as string)).toThrow('Invalid WBS code');
	});
});

describe('buildCategoryPrefix', () => {
	it('should build prefix from category stack', () => {
		const stack = ['Site Work', 'Excavation'];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});

	it('should handle empty stack', () => {
		const prefix = buildCategoryPrefix([]);
		expect(prefix).toBe('');
	});

	it('should trim category names', () => {
		const stack = [' Site Work ', ' Excavation '];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});
});

describe('classifyRow', () => {
	const mockMapping: ColumnMapping = {
		code: 0,
		description: 1,
		quantity: 2,
	};

	it('should classify detail rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			rawValues: ['1.1.1', 'Concrete work', 100],
		};
		expect(classifyRow(row, mockMapping)).toBe('detail');
	});

	it('should classify category rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			rawValues: ['', 'Site Work', null],
		};
		expect(classifyRow(row, mockMapping)).toBe('category');
	});

	it('should classify summary rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			rawValues: ['', 'TOTAL Site Work', null],
		};
		expect(classifyRow(row, mockMapping)).toBe('summary');
	});

	it('should classify ignore rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			rawValues: ['', '', null],
		};
		expect(classifyRow(row, mockMapping)).toBe('ignore');
	});
});

describe('collectIsolatedCategories', () => {
	const mockMapping: ColumnMapping = {
		code: 0,
		description: 1,
		quantity: 2,
	};

	it('should collect categories with blank rows above and below', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 1, rawValues: ['', 'Isolated Category A', null] }, // Isolated
			{ originalIndex: 2, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 3, rawValues: ['', 'Connected Category', null] }, // Not isolated (no blank above)
			{ originalIndex: 4, rawValues: ['1.1', 'Detail', 100] },
			{ originalIndex: 5, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 6, rawValues: ['', 'Isolated Category B', null] }, // Isolated
			{ originalIndex: 7, rawValues: ['', '', null] }, // Blank
		];

		const result = collectIsolatedCategories(rows, mockMapping);
		expect(result).toEqual([
			{ name: 'Isolated Category A', rowIndex: 1 },
			{ name: 'Isolated Category B', rowIndex: 6 },
		]);
	});

	it('should not collect categories that are not isolated', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Category A', null] }, // No blank above
			{ originalIndex: 1, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 2, rawValues: ['', 'Category B', null] }, // No blank below
			{ originalIndex: 3, rawValues: ['1.1', 'Detail', 100] },
		];

		const result = collectIsolatedCategories(rows, mockMapping);
		expect(result).toEqual([]);
	});

	it('should maintain order and avoid duplicates', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 1, rawValues: ['', 'Category A', null] }, // Isolated
			{ originalIndex: 2, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 3, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 4, rawValues: ['', 'Category A', null] }, // Isolated (duplicate name)
			{ originalIndex: 5, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 6, rawValues: ['', '', null] }, // Blank
			{ originalIndex: 7, rawValues: ['', 'Category B', null] }, // Isolated
			{ originalIndex: 8, rawValues: ['', '', null] }, // Blank
		];

		const result = collectIsolatedCategories(rows, mockMapping);
		expect(result).toEqual([
			{ name: 'Category A', rowIndex: 1 },
			{ name: 'Category A', rowIndex: 4 },
			{ name: 'Category B', rowIndex: 7 },
		]);
	});
});

describe('applyCategoriesToRows', () => {
	const mockMapping: ColumnMapping = {
		code: 0,
		description: 1,
		quantity: 2,
	};

	it('should automatically apply category prefixes when category is immediately followed by detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Excavation', 100] },
			{ originalIndex: 2, rawValues: ['1.2', 'Grading', 50] },
		];

		const result = applyCategoriesToRows(rows, mockMapping);

		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work] Excavation');
		expect(result[2].categoryPrefix).toBe('[Site Work]');
		expect(result[2].finalDescription).toBe('[Site Work] Grading');
	});

	it('should NOT automatically apply category prefixes when category has blank rows after it', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 1, rawValues: ['', '', null] }, // Blank row
			{ originalIndex: 2, rawValues: ['1.1', 'Excavation', 100] },
		];

		const result = applyCategoriesToRows(rows, mockMapping);

		expect(result[0].classification).toBe('category');
		expect(result[2].classification).toBe('detail');
		expect(result[2].categoryPrefix).toBeUndefined();
		expect(result[2].finalDescription).toBe('Excavation');
	});

	it('should handle category edits', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Excavation', 100] },
		];

		const categoryEdits = { 0: 'Modified Site Work' };
		const result = applyCategoriesToRows(rows, mockMapping, categoryEdits);

		expect(result[1].categoryPrefix).toBe('[Modified Site Work]');
		expect(result[1].finalDescription).toBe('[Modified Site Work] Excavation');
	});

	it('should handle manual category selections for category rows and flow them to detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Category A', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Detail Item', 100] },
		];

		const manualCategorySelections = { 0: ['Manual Cat 1', 'Manual Cat 2'] };
		const result = applyCategoriesToRows(rows, mockMapping, {}, manualCategorySelections);

		expect(result[0].manualCategories).toEqual(['Manual Cat 1', 'Manual Cat 2']);
		expect(result[0].finalDescription).toBe('[Manual Cat 1][Manual Cat 2] Category A');
		// Detail row should get both manual categories AND the automatic category
		expect(result[1].categoryPrefix).toBe('[Manual Cat 1][Manual Cat 2][Category A]');
		expect(result[1].finalDescription).toBe('[Manual Cat 1][Manual Cat 2][Category A] Detail Item');
	});

	it('should reset automatic category prefix when encountering blank rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Excavation', 100] },
			{ originalIndex: 2, rawValues: ['', '', null] }, // Empty row - should reset automatic prefix
			{ originalIndex: 3, rawValues: ['', 'Concrete Work', null] },
			{ originalIndex: 4, rawValues: ['2.1', 'Foundation', 50] },
		];

		const result = applyCategoriesToRows(rows, mockMapping);

		// First category and detail row
		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work] Excavation');

		// Empty row
		expect(result[2].classification).toBe('ignore');

		// Second category and detail row (should have Concrete Work prefix)
		expect(result[3].classification).toBe('category');
		expect(result[4].classification).toBe('detail');
		expect(result[4].categoryPrefix).toBe('[Concrete Work]');
		expect(result[4].finalDescription).toBe('[Concrete Work] Foundation');
	});

	it('should combine manual categories with automatic category prefixes', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Excavation', 100] },
		];

		const manualCategorySelections = { 0: ['Phase 1', 'Area A'] };
		const result = applyCategoriesToRows(rows, mockMapping, {}, manualCategorySelections);

		// Category row should show manual categories in final description
		expect(result[0].manualCategories).toEqual(['Phase 1', 'Area A']);
		expect(result[0].finalDescription).toBe('[Phase 1][Area A] Site Work');

		// Detail row should get both manual categories AND the automatic category prefix
		expect(result[1].categoryPrefix).toBe('[Phase 1][Area A][Site Work]');
		expect(result[1].finalDescription).toBe('[Phase 1][Area A][Site Work] Excavation');
	});

	it('should handle mixed scenarios with manual and automatic categories', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Phase 1', null] },
			{ originalIndex: 1, rawValues: ['', '', null] }, // Blank - Phase 1 won't be automatically applied
			{ originalIndex: 2, rawValues: ['', 'Site Work', null] },
			{ originalIndex: 3, rawValues: ['1.1', 'Excavation', 100] },
			{ originalIndex: 4, rawValues: ['', '', null] }, // Blank - reset automatic prefix
			{ originalIndex: 5, rawValues: ['', 'Concrete Work', null] },
			{ originalIndex: 6, rawValues: ['', '', null] }, // Blank - Concrete Work won't be automatically applied
			{ originalIndex: 7, rawValues: ['2.1', 'Foundation', 50] },
		];

		const manualCategorySelections = { 2: ['Phase 1'] }; // Manually add Phase 1 to Site Work
		const result = applyCategoriesToRows(rows, mockMapping, {}, manualCategorySelections);

		// Site Work shows manual category in final description
		expect(result[2].manualCategories).toEqual(['Phase 1']);
		expect(result[2].finalDescription).toBe('[Phase 1] Site Work');

		// Excavation gets both manual and automatic prefix
		expect(result[3].categoryPrefix).toBe('[Phase 1][Site Work]');
		expect(result[3].finalDescription).toBe('[Phase 1][Site Work] Excavation');

		// Concrete Work has no manual categories
		expect(result[5].manualCategories).toBeUndefined();
		expect(result[5].finalDescription).toBe('Concrete Work');

		// Foundation has no automatic prefix (blank row after Concrete Work)
		expect(result[7].categoryPrefix).toBeUndefined();
		expect(result[7].finalDescription).toBe('Foundation');
	});

	it('should flow manual categories through to detail rows in complex scenarios', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, rawValues: ['', 'Electrical Work', null] },
			{ originalIndex: 1, rawValues: ['1.1', 'Wiring', 100] },
			{ originalIndex: 2, rawValues: ['1.2', 'Outlets', 50] },
			{ originalIndex: 3, rawValues: ['', '', null] }, // Blank - reset automatic prefix
			{ originalIndex: 4, rawValues: ['', 'Plumbing Work', null] },
			{ originalIndex: 5, rawValues: ['', '', null] }, // Blank - no automatic application
			{ originalIndex: 6, rawValues: ['2.1', 'Pipes', 75] },
		];

		const manualCategorySelections = {
			0: ['Building A', 'Floor 1'], // Manual categories for Electrical Work
			4: ['Building A', 'Floor 2'], // Manual categories for Plumbing Work (but won't flow to detail)
		};
		const result = applyCategoriesToRows(rows, mockMapping, {}, manualCategorySelections);

		// Electrical Work category shows manual categories
		expect(result[0].manualCategories).toEqual(['Building A', 'Floor 1']);
		expect(result[0].finalDescription).toBe('[Building A][Floor 1] Electrical Work');

		// Wiring gets full hierarchy: manual categories + automatic category
		expect(result[1].categoryPrefix).toBe('[Building A][Floor 1][Electrical Work]');
		expect(result[1].finalDescription).toBe('[Building A][Floor 1][Electrical Work] Wiring');

		// Outlets also gets full hierarchy
		expect(result[2].categoryPrefix).toBe('[Building A][Floor 1][Electrical Work]');
		expect(result[2].finalDescription).toBe('[Building A][Floor 1][Electrical Work] Outlets');

		// Plumbing Work shows manual categories but won't flow to detail (blank row after)
		expect(result[4].manualCategories).toEqual(['Building A', 'Floor 2']);
		expect(result[4].finalDescription).toBe('[Building A][Floor 2] Plumbing Work');

		// Pipes has no automatic prefix (blank row after Plumbing Work)
		expect(result[6].categoryPrefix).toBeUndefined();
		expect(result[6].finalDescription).toBe('Pipes');
	});
});

describe('validateColumnMapping', () => {
	it('should validate required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			quantity: 2,
			material_rate: 3,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(true);
		expect(result.errors).toEqual([]);
	});

	it('should detect missing required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			// missing quantity and rate
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('Quantity is required');
		expect(result.errors).toContain('Material Rate is required');
	});

	it('should detect duplicate mappings', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 0, // duplicate index
			quantity: 1,
			material_rate: 2,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('Column index 0 is mapped to multiple fields');
	});
});

describe('transformToImportData', () => {
	const mockMapping: ColumnMapping = {
		code: 0,
		description: 1,
		quantity: 2,
		uom: 3,
		material_rate: 4,
		factor: 5,
	};

	it('should transform classified rows to import format', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				rawValues: ['', 'Site Work', null, null, null, null],
				classification: 'category' as const,
				finalDescription: 'Site Work',
			},
			{
				originalIndex: 1,
				rawValues: ['1.1', 'Excavation', 100, 'm3', 50, 1.1],
				classification: 'detail' as const,
				finalDescription: '[Site Work]Excavation',
			},
			{
				originalIndex: 2,
				rawValues: ['', '', null, null, null, null],
				classification: 'ignore' as const,
			},
		];

		const result = transformToImportData(classifiedRows, mockMapping, 'project-123');

		expect(result.project_id).toBe('project-123');
		expect(result.items).toHaveLength(1);
		expect(result.items[0]).toEqual({
			code: '1.1',
			description: '[Site Work]Excavation',
			quantity: 100,
			unit: 'm3',
			material_rate: 50,
			factor: 1.1,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			unit_rate_manual_override: false,
			unit_rate: undefined,
			parentFactor: undefined,
			remarks: undefined,
		});
	});

	it('should handle missing values gracefully', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				rawValues: ['1.1', 'Test item', null, null, null, null],
				classification: 'detail' as const,
				finalDescription: 'Test item',
				// missing quantity, rate, etc.
			},
		];

		const result = transformToImportData(classifiedRows, mockMapping, 'project-123');

		expect(result.items[0]).toEqual({
			code: '1.1',
			description: 'Test item',
			quantity: 0, // Raw value from data (null becomes 0)
			unit: '',
			material_rate: 0,
			factor: 1, // Default factor value
			labor_rate: undefined,
			productivity_per_hour: undefined,
			unit_rate_manual_override: false,
			unit_rate: undefined,
			parentFactor: undefined,
			remarks: undefined,
		});
	});

	it('should handle nested categories with identical descriptions correctly', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				rawValues: ['7.1', 'Frame', null, null, null, null],
				classification: 'detail' as const,
				finalDescription: 'Frame',
			},
			{
				originalIndex: 1,
				rawValues: ['', 'TOTAL Frame', null, null, null, 1.5],
				classification: 'summary' as const,
			},
			{
				originalIndex: 2,
				rawValues: ['7.1.1', 'Frame', null, null, null, null],
				classification: 'detail' as const,
				finalDescription: 'Frame',
			},
			{
				originalIndex: 3,
				rawValues: ['', 'TOTAL Frame', null, null, null, 2.0],
				classification: 'summary' as const,
			},
		];

		const result = transformToImportData(classifiedRows, mockMapping, 'project-123');

		expect(result.items).toHaveLength(2);

		// The outer category (7.1) should get factor 1.5 from the first TOTAL
		const outerItem = result.items.find((item) => item.code === '7.1');
		expect(outerItem?.factor).toBe(1.5);

		// The inner category (7.1.1) should get factor 2.0 from the second TOTAL
		const innerItem = result.items.find((item) => item.code === '7.1.1');
		expect(innerItem?.factor).toBe(2.0);
	});

	it('should calculate parentFactor correctly using factors from summary rows', () => {
		const classifiedRows = [
			// Level 1 category
			{
				originalIndex: 0,
				rawValues: ['1', 'Foundation', null, null, null, null],
				classification: 'detail' as const,
				finalDescription: 'Foundation',
			},
			{
				originalIndex: 1,
				rawValues: ['', 'TOTAL Foundation', null, null, null, 1.2],
				classification: 'summary' as const,
			},
			// Level 2 category
			{
				originalIndex: 2,
				rawValues: ['1.1', 'Excavation', null, null, null, null],
				classification: 'detail' as const,
				finalDescription: 'Excavation',
			},
			{
				originalIndex: 3,
				rawValues: ['', 'TOTAL Excavation', null, null, null, 1.5],
				classification: 'summary' as const,
			},
			// Level 3 detail item
			{
				originalIndex: 4,
				rawValues: ['1.1.1', 'Site Preparation', 100, 'm3', 50, 1.1],
				classification: 'detail' as const,
				finalDescription: 'Site Preparation',
			},
		];

		const result = transformToImportData(classifiedRows, mockMapping, 'project-123');

		expect(result.items).toHaveLength(3);

		// Level 1 (1) should have no parentFactor
		const level1Item = result.items.find((item) => item.code === '1');
		expect(level1Item?.factor).toBe(1.2);
		expect(level1Item?.parentFactor).toBeUndefined();

		// Level 2 (1.1) should have parentFactor = 1.2 (from parent "1")
		const level2Item = result.items.find((item) => item.code === '1.1');
		expect(level2Item?.factor).toBe(1.5);
		expect(level2Item?.parentFactor).toBe(1.2);

		// Level 3 (1.1.1) should have parentFactor = 1.2 * 1.5 = 1.8 (from parents "1" and "1.1")
		const level3Item = result.items.find((item) => item.code === '1.1.1');
		expect(level3Item?.factor).toBe(1.1);
		expect(level3Item?.parentFactor).toBeCloseTo(1.8); // 1.2 * 1.5
	});
});
