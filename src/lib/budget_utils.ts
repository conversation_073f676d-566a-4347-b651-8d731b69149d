import type { Tables } from '$lib/database.types';
import type { UpdateBudgetItem } from './schemas/project';
import { stratify, type HierarchyNode } from 'd3-hierarchy';

// 1) Raw data shape coming from supabase.from('wbs_library_item').select(...)
export interface RawNode extends Tables<'wbs_library_item'> {
	budget_line_item_current: Tables<'budget_line_item_current'>[];
}

// Budget snapshot related interfaces
export interface BudgetSnapshotLineItem extends Tables<'budget_snapshot_line_item'> {
	wbs_library_item?: Tables<'wbs_library_item'>;
}

export interface BudgetSnapshot extends Tables<'budget_snapshot'> {
	project_stage?: {
		name: string;
		stage_order: number;
		stage: number | null;
		project_id: string;
	};
}

// 2) Node augmented with its computed costs and children
export interface NodeWithCosts extends RawNode {
	children: NodeWithCosts[];
	directCost: number;
	childrenCost: number;
	totalCost: number;
}

export interface EnhancedWbsItemTree extends Tables<'wbs_library_item'> {
	budgetItems: RawNode['budget_line_item_current'];
	directCost: number;
	childrenCost: number;
	totalCost: number;
	children: EnhancedWbsItemTree[];
	nodeId: string; // e.g. `node-${wbs_library_item_id}`
}

// Base interface for budget line items with common properties
export interface BudgetLineItemBase {
	wbs_library_item_id: string;
	quantity: number | null;
	unit_rate: number | null;
	factor: number | null;
}

// Generic interface for WBS items with budget data
export interface WbsItemWithBudgetData<T extends BudgetLineItemBase>
	extends Tables<'wbs_library_item'> {
	budgetData?: T;
	subtotal: number;
	value: number; // Used by stratify for automatic totaling
	totalFactor: number; // Cumulative factor from current item and all ancestors
}

export interface BudgetHierarchyNode<
	T extends BudgetLineItemBase = Tables<'budget_snapshot_line_item'>,
> {
	id: string;
	parentId: string | null;
	data: WbsItemWithBudgetData<T>;
	value: number;
	children?: BudgetHierarchyNode<T>[];
	depth: number;
}

export function buildBudgetTree(raw: RawNode[]): EnhancedWbsItemTree[] {
	// A) Clone each raw node into an EnhancedWbsItemTree skeleton
	const map = new Map<string, EnhancedWbsItemTree>();
	raw.forEach((r) => {
		map.set(r.wbs_library_item_id, {
			...r, // all wbs_library_item columns
			budgetItems: r.budget_line_item_current,
			directCost: 0,
			childrenCost: 0,
			totalCost: 0,
			children: [],
			nodeId: `node-${r.wbs_library_item_id}`,
		});
	});

	// B) Hook up parent ↔ child
	const roots: EnhancedWbsItemTree[] = [];
	map.forEach((node) => {
		if (node.parent_item_id && map.has(node.parent_item_id)) {
			map.get(node.parent_item_id)!.children.push(node);
		} else {
			roots.push(node);
		}
	});

	// C) Compute costs bottom-up
	function computeCosts(node: EnhancedWbsItemTree) {
		// directCost = sum(quantity * unit_rate)
		node.directCost = node.budgetItems.reduce(
			(sum, bi) => sum + bi.quantity * bi.unit_rate * (bi.factor ?? 1),
			0,
		);

		// first compute children, accumulate their totalCost
		node.children.forEach((child) => {
			computeCosts(child);
			node.childrenCost += child.totalCost;
		});

		// total = direct + children
		node.totalCost = node.directCost + node.childrenCost;
	}
	roots.forEach(computeCosts);

	return roots;
}

export function calculateUnitRate(
	item: Partial<RawNode['budget_line_item_current'][number]>,
): number {
	if (item.unit_rate_manual_override) {
		if (!item.unit_rate) {
			item.unit_rate = 0;
		}
		// If unit rate is manually overridden, return the manual value
		return item.unit_rate;
	}

	const materialCost = item.material_rate || 0;
	const laborCost = item.labor_rate || 0;
	const productivity = item.productivity_per_hour || 0;

	// If productivity is provided, calculate labor cost per unit
	const laborCostPerUnit = productivity > 0 ? laborCost / productivity : 0;

	return materialCost + laborCostPerUnit;
}

/**
 * Calculate subtotal for a budget item using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateSubtotal(
	snapshotData: Tables<'budget_snapshot_line_item'> | undefined,
): number {
	if (!snapshotData) return 0;
	return (snapshotData.quantity || 0) * (snapshotData.unit_rate || 0) * (snapshotData.factor ?? 1);
}

/**
 * Generic calculate subtotal for any budget line item type using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateBudgetItemSubtotal<T extends BudgetLineItemBase>(
	budgetItem: T | undefined,
): number {
	if (!budgetItem) return 0;
	return (budgetItem.quantity || 0) * (budgetItem.unit_rate || 0) * (budgetItem.factor ?? 1);
}

/**
 * Calculate total factors for all WBS items, considering hierarchical factor inheritance
 */
function calculateTotalFactors<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItemMap: Map<string, T>,
): Map<string, number> {
	const totalFactorMap = new Map<string, number>();

	// Create a map of children by parent for efficient lookup
	const childrenMap = new Map<string, Tables<'wbs_library_item'>[]>();
	wbsItems.forEach((item) => {
		const parentId = item.parent_item_id;
		if (parentId) {
			if (!childrenMap.has(parentId)) {
				childrenMap.set(parentId, []);
			}
			childrenMap.get(parentId)!.push(item);
		}
	});

	// Recursive function to calculate total factor for an item
	function calculateTotalFactor(item: Tables<'wbs_library_item'>): number {
		// Check if already calculated
		if (totalFactorMap.has(item.wbs_library_item_id)) {
			return totalFactorMap.get(item.wbs_library_item_id)!;
		}

		// Get the item's own factor from budget data
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const itemFactor = budgetData?.factor ?? 1;

		// Calculate parent's total factor
		let parentTotalFactor = 1;
		if (item.parent_item_id) {
			const parentItem = wbsItems.find((w) => w.wbs_library_item_id === item.parent_item_id);
			if (parentItem) {
				parentTotalFactor = calculateTotalFactor(parentItem);
			}
		}

		// Total factor is parent's total factor multiplied by this item's factor
		const totalFactor = parentTotalFactor * itemFactor;
		totalFactorMap.set(item.wbs_library_item_id, totalFactor);

		return totalFactor;
	}

	// Calculate total factors for all items
	wbsItems.forEach((item) => calculateTotalFactor(item));

	return totalFactorMap;
}

/**
 * Create hierarchical budget structure using stratify - generic version
 */
export function createGenericBudgetHierarchy<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItems: T[],
) {
	// Create a map of budget data by WBS item ID
	const budgetItemMap = new Map<string, T>();
	budgetItems.forEach((item) => {
		budgetItemMap.set(item.wbs_library_item_id, item);
	});

	// Calculate total factors for all items
	const totalFactorMap = calculateTotalFactors(wbsItems, budgetItemMap);

	// Transform WBS items to include budget data and calculated values
	const itemsWithData: WbsItemWithBudgetData<T>[] = wbsItems.map((item) => {
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const subtotal = calculateBudgetItemSubtotal(budgetData);
		const totalFactor = totalFactorMap.get(item.wbs_library_item_id) ?? 1;

		// For stratify value calculation:
		// - If item has budget data: use quantity * unit_rate (without factor)
		// - The totalFactor will be applied during stratify summation
		let value = 0;
		if (budgetData) {
			value = (budgetData.quantity || 0) * (budgetData.unit_rate || 0);
		}

		return {
			...item,
			budgetData,
			subtotal,
			value,
			totalFactor,
		};
	});

	// Create a project budget root node to contain all WBS items
	const projectBudgetRoot: WbsItemWithBudgetData<T> = {
		wbs_library_item_id: '__project_budget_root__',
		wbs_library_id: '__project_budget_root__',
		level: 0,
		in_level_code: '0',
		parent_item_id: null,
		code: '0',
		description: 'Project Budget Total',
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '',
		updated_at: '',
		budgetData: undefined,
		subtotal: 0,
		value: 0,
		totalFactor: 1,
	};

	// Add project budget root and update parent references for root items
	const allItems = [
		projectBudgetRoot,
		...itemsWithData.map((item) => ({
			...item,
			parent_item_id: item.parent_item_id || '__project_budget_root__',
		})),
	];

	// Use stratify to create hierarchical structure with proper value calculation
	const stratifyFn = stratify<WbsItemWithBudgetData<T>>()
		.id((d) => d.wbs_library_item_id)
		.parentId((d) => d.parent_item_id);

	// Create the hierarchy and apply sum calculation that accounts for totalFactor
	const root = stratifyFn(allItems).sum((d) => {
		// For items with budget data, use base value multiplied by totalFactor
		if (d.budgetData) {
			return d.value * d.totalFactor;
		}
		// For items without budget data (categories), return 0 so they don't contribute to sum
		return 0;
	});

	// Fix subtotal calculation for category items after hierarchy is built
	function fixCategorySubtotals(node: HierarchyNode<WbsItemWithBudgetData<T>>): void {
		// If this is a category item (has children but no budget data or has budget data with no quantity/rate)
		if (node.children && node.children.length > 0) {
			const hasOwnBudgetData =
				node.data.budgetData && (node.data.budgetData.quantity || node.data.budgetData.unit_rate);

			if (!hasOwnBudgetData) {
				// Calculate sum of children's subtotals
				const childrenSum = node.children.reduce(
					(sum: number, child) => sum + child.data.subtotal,
					0,
				);

				// Apply this item's own factor (if any) to the children sum
				const ownFactor = node.data.budgetData?.factor ?? 1;
				node.data.subtotal = childrenSum * ownFactor;
			}
		}

		// Recursively fix children
		if (node.children) {
			node.children.forEach(fixCategorySubtotals);
		}
	}

	// Apply the fix to the entire hierarchy
	fixCategorySubtotals(root);

	// If there's only one root WBS item, return it directly for cleaner UI
	// Otherwise return the project budget root that contains multiple items
	if (root.children && root.children.length === 1) {
		return root.children[0];
	}

	return root;
}

/**
 * Create hierarchical budget structure for current budget line items
 */
export function createCurrentBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	currentItems: Tables<'budget_line_item_current'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, currentItems);
}

/**
 * Create hierarchical budget structure for budget snapshots using the generic version
 */
export function createSnapshotBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotItems: Tables<'budget_snapshot_line_item'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, snapshotItems);
}

export type SnapshotBudgetNode = ReturnType<
	typeof createGenericBudgetHierarchy<Tables<'budget_snapshot_line_item'>>
>;
export type CurrentBudgetNode = ReturnType<typeof createCurrentBudgetHierarchy>;

// Enhanced BudgetLineItem with UI state
export interface BudgetLineItem extends Partial<UpdateBudgetItem> {
	budget_line_item_id?: string;
	project_id: string;
	wbs_library_item_id: string;
	quantity: number;
	unit: string | null;
	material_rate: number;
	labor_rate: number | null;
	productivity_per_hour: number | null;
	unit_rate_manual_override: boolean;
	unit_rate: number;
	remarks: string | null;
	cost_certainty: number | null;
	design_certainty: number | null;
	created_at?: string;
	updated_at?: string;
	// UI-specific fields
	wbs_code?: string;
	description?: string;
	subtotal?: number;
	extension?: number;
	isParent?: boolean;
	level?: number;
}
