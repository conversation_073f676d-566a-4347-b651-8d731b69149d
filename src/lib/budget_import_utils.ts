/**
 * Type for items returned by transformToImportData - guarantees required fields
 */
export interface ImportItem {
	code: string;
	description: string;
	quantity: number;
	unit: string;
	material_rate: number;
	labor_rate?: number;
	productivity_per_hour?: number;
	unit_rate_manual_override: boolean;
	unit_rate?: number;
	factor?: number;
	parentFactor?: number;
	remarks?: string;
}

/**
 * Type for ImportItem that's compatible with Supabase Json type
 * This adds the index signature required by the Json type
 */
export type ImportItemJson = ImportItem & { [key: string]: Json | undefined };

// Import the Json type from database types
import type { Json } from './database.types';

export type ExcelRow = Record<string, string | number | null>;

export type ProcessedRow = {
	originalIndex: number;
	// Raw cell values indexed by column position
	rawValues: (string | number | null)[];
	hasYellowFill?: boolean;
};

export type ClassifiedRow = ProcessedRow & {
	classification: 'detail' | 'summary' | 'category' | 'ignore';
	categoryPrefix?: string;
	finalDescription?: string;
	parentFactor?: number;
	sumOfTotals?: number; // some rows have a Total but no rate, quantity, or subtotal
	manualCategories?: string[]; // For category rows: manually selected categories to prepend
};

export type ColumnMapping = {
	code?: number;
	description?: number;
	quantity?: number;
	uom?: number;
	material_rate?: number;
	labor_rate?: number;
	productivity_per_hour?: number;
	unit_rate_manual_override?: number;
	unit_rate?: number;
	factor?: number;
	cost_certainty?: number;
	design_certainty?: number;
	first_total?: number;
	last_total?: number;
	// Legacy fields for backward compatibility
	rate?: number;
	subtotal?: number;
};

export interface ApplicationField {
	key: keyof ColumnMapping;
	label: string;
	required: boolean;
	conditionallyRequired?: (mapping: ColumnMapping) => boolean;
}

/**
 * First RegExp to match a header wins (case‑insensitive).
 * List patterns most‑specific → least‑specific.
 */
export const COLUMN_PATTERNS: Record<keyof ColumnMapping, RegExp[]> = {
	code: [/^code$/i, /^wbs\.?code$/i, /\bwbs\b/i, /\bcost\s*code\b/i],
	description: [/^description$/i, /^desc$/i, /\b(scope|item\s*name)\b/i],
	quantity: [/^quantity$/i, /^qty$/i, /\bq(?:uantity)?ty?\b/i],
	uom: [/^uom$/i, /^unit(?:\s*of\s*measure)?$/i, /^units?$/i],
	// Legacy patterns for backward compatibility - put rate before material_rate to match tests
	rate: [/^rate$/i, /\brate\b/i],
	material_rate: [/^material\s*rate$/i, /\bmaterial\s*cost\b/i, /\bmat\s*rate\b/i],
	labor_rate: [/^labor\s*rate$/i, /^labour\s*rate$/i, /\blabor\s*cost\b/i, /\blabour\s*cost\b/i],
	productivity_per_hour: [
		/^productivity$/i,
		/\bproductivity\s*per\s*hour\b/i,
		/\bunits?\s*per\s*hour\b/i,
	],
	unit_rate_manual_override: [/^manual\s*override$/i, /\boverride\b/i, /\bmanual\s*rate\b/i],
	unit_rate: [/^unit\s*rate$/i, /\btotal\s*rate\b/i, /\bfinal\s*rate\b/i],
	factor: [/^factor$/i, /\bmultiplier\b/i, /\badjust(?:ment)?\s*factor\b/i],
	cost_certainty: [/^cost\s*certainty$/i, /\bcost\s*confidence\b/i],
	design_certainty: [/^design\s*certainty$/i, /\bdesign\s*confidence\b/i],
	subtotal: [/^sub\s*total$/i, /^subtotal$/i, /\bsub[-\s]*total\b/i],
	first_total: [/^total$/i],
	last_total: [/^total$/i],
};

/**
 * Application field definitions for the new column classifier
 */
export const APPLICATION_FIELDS: ApplicationField[] = [
	{ key: 'code', label: 'WBS Code', required: true },
	{ key: 'description', label: 'Scope Description', required: true },
	{ key: 'quantity', label: 'Quantity', required: true },
	{ key: 'uom', label: 'Unit', required: false },
	{ key: 'material_rate', label: 'Material Rate', required: true },
	{ key: 'labor_rate', label: 'Labor Rate', required: false },
	{ key: 'productivity_per_hour', label: 'Productivity Per Hour', required: false },
	{ key: 'unit_rate_manual_override', label: 'Unit Rate Manual Override', required: false },
	{
		key: 'unit_rate',
		label: 'Unit Rate',
		required: false,
		conditionallyRequired: (mapping) => !!mapping.unit_rate_manual_override,
	},
	{ key: 'factor', label: 'Factor', required: false },
	{ key: 'cost_certainty', label: 'Cost Certainty', required: false },
	{ key: 'design_certainty', label: 'Design Certainty', required: false },
];

/**
 * Automatically map column headers to their types using regex patterns
 */
export function matchColumns(headers: string[]): ColumnMapping {
	const mapping: ColumnMapping = {};

	headers.forEach((header, index) => {
		if (!header || typeof header !== 'string') return;

		const cleanHeader = header.trim();
		if (!cleanHeader) return;

		// Check each column type
		for (const [columnType, patterns] of Object.entries(COLUMN_PATTERNS)) {
			// Skip if we already found a match for this column type
			if (mapping[columnType as keyof ColumnMapping] !== undefined) continue;

			// Check if any pattern matches
			const matches = patterns.some((pattern) => pattern.test(cleanHeader));
			if (matches) {
				mapping[columnType as keyof ColumnMapping] = index;
				break; // Stop checking other patterns for this header
			}
		}
	});
	const lastTotalIndex = headers.findLastIndex((header) => header.trim().toLowerCase() === 'total');
	if (lastTotalIndex !== -1) {
		mapping.last_total = lastTotalIndex;
	}

	return mapping;
}

/**
 * Parse WBS code into hierarchical components
 */
export function parseWbsCode(code: string): {
	level: number;
	in_level_code: string;
	parent_code: string | null;
} {
	if (!code || typeof code !== 'string') {
		throw new Error('Invalid WBS code');
	}

	const parts = code.split('.');
	const level = parts.length;
	const in_level_code = parts[parts.length - 1];
	const parent_code = level > 1 ? parts.slice(0, -1).join('.') : null;

	return {
		level,
		in_level_code,
		parent_code,
	};
}

/**
 * Build category prefix from category stack
 */
export function buildCategoryPrefix(categoryStack: string[]): string {
	if (categoryStack.length === 0) return '';

	return categoryStack.map((category) => `[${category.trim()}]`).join('');
}

/**
 * Extract a specific field value from a ProcessedRow using column mapping
 */
export function getRowValue(
	row: ProcessedRow,
	field: keyof ColumnMapping,
	mapping: ColumnMapping,
): string | number | null {
	const columnIndex = mapping[field];
	if (columnIndex === undefined || columnIndex >= row.rawValues.length) {
		return null;
	}
	return row.rawValues[columnIndex];
}

/**
 * Get string value from row with fallback to empty string
 */
export function getRowStringValue(
	row: ProcessedRow,
	field: keyof ColumnMapping,
	mapping: ColumnMapping,
): string {
	const value = getRowValue(row, field, mapping);
	return value ? String(value).trim() : '';
}

/**
 * Get numeric value from row with fallback to 0
 */
export function getRowNumericValue(
	row: ProcessedRow,
	field: keyof ColumnMapping,
	mapping: ColumnMapping,
	defaultValue: number = 0,
): number {
	const value = getRowValue(row, field, mapping);
	if (value === null || value === undefined || value === '') return defaultValue;
	const parsed = parseFloat(String(value));
	return Number.isNaN(parsed) ? defaultValue : parsed;
}
/**
 * Get sum of all "total" header columns in a row
 */
export function getSumOfTotals(row: ProcessedRow, mapping: ColumnMapping): number {
	const firstTotalIndex = mapping['first_total'];
	const lastTotalIndex = mapping['last_total'];
	if (firstTotalIndex === undefined || lastTotalIndex === undefined) return 0;
	const totalValues = row.rawValues.slice(firstTotalIndex, lastTotalIndex + 1);
	return totalValues.reduce<number>((acc, value) => {
		if (value === null || value === undefined || value === '') return acc;
		const parsed = parseFloat(String(value));
		return acc + (Number.isNaN(parsed) ? 0 : parsed);
	}, 0);
}

/**
 * Classify a row based on its content using column mapping
 */
export function classifyRow(
	row: ProcessedRow,
	mapping: ColumnMapping,
): ClassifiedRow['classification'] {
	const code = getRowStringValue(row, 'code', mapping);
	const description = getRowStringValue(row, 'description', mapping);

	// Detail row - has a code
	if (code && code.trim()) {
		return 'detail';
	}

	// Summary row - description starts with "TOTAL"
	if (description && description.trim().toUpperCase().startsWith('TOTAL ')) {
		return 'summary';
	}

	// Category row - has description but no code, usually surrounded by empty rows
	if (description && description.trim() && !code) {
		return 'category';
	}

	// Everything else is ignored (headers, empty rows, etc.)
	return 'ignore';
}

/**
 * Collect isolated categories (categories with blank rows above AND below)
 */
export function collectIsolatedCategories(
	rows: ProcessedRow[],
	mapping: ColumnMapping,
): { name: string; rowIndex: number }[] {
	// TODO: this can be improved by segregating by the WBS code hierarchy
	const isolatedCategories: { name: string; rowIndex: number }[] = [];

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row, mapping);

		if (classification === 'category') {
			// Check if this category has blank rows both above and below
			const hasBlankAbove = i > 0 && isBlankRow(rows[i - 1], mapping);
			const hasBlankBelow = i < rows.length - 1 && isBlankRow(rows[i + 1], mapping);
			const hasCategoryBelow =
				i < rows.length - 1 && classifyRow(rows[i + 1], mapping) === 'category';

			if (hasBlankAbove && (hasBlankBelow || hasCategoryBelow)) {
				const categoryName = getRowStringValue(row, 'description', mapping);
				if (categoryName) {
					isolatedCategories.push({ name: categoryName, rowIndex: i });
				}
			}
		}
	}

	return isolatedCategories;
}

/**
 * Check if a row is blank (ignore classification with no description)
 */
function isBlankRow(row: ProcessedRow, mapping: ColumnMapping): boolean {
	const classification = classifyRow(row, mapping);
	const description = getRowStringValue(row, 'description', mapping);
	return classification === 'ignore' && !description.trim();
}

/**
 * Apply simplified category logic with manual category selections
 */
export function applyCategoriesToRows(
	rows: ProcessedRow[],
	mapping: ColumnMapping,
	categoryEdits: Record<number, string> = {},
	manualCategorySelections: Record<number, string[]> = {},
): ClassifiedRow[] {
	const result: ClassifiedRow[] = [];
	let currentCategoryPrefix = '';

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row, mapping);

		const classifiedRow: ClassifiedRow = {
			...row,
			classification,
		};

		if (classification === 'category') {
			// Use edited category name if available, otherwise original description
			const originalDescription = getRowStringValue(row, 'description', mapping);
			const categoryName = categoryEdits[i] || originalDescription;

			// Get manual category selections for this row
			const manualCategories = manualCategorySelections[i] || [];
			if (manualCategories.length > 0) {
				classifiedRow.manualCategories = manualCategories;
			}

			// Build final description with manual categories prepended
			const manualPrefix = manualCategories.map((cat) => `[${cat.trim()}]`).join('');
			classifiedRow.finalDescription = manualPrefix + (manualPrefix ? ' ' : '') + categoryName;

			// Check if this category should be automatically applied (immediately followed by detail rows)
			const nextRowIndex = i + 1;
			if (nextRowIndex < rows.length && classifyRow(rows[nextRowIndex], mapping) === 'detail') {
				// Automatically apply this category to subsequent detail rows
				// Include both manual categories and the category itself in the prefix
				const fullPrefix = manualPrefix + `[${categoryName.trim()}]`;
				currentCategoryPrefix = fullPrefix;
			} else {
				// This category is not automatically applied (has blank rows after it)
				currentCategoryPrefix = '';
			}
		} else if (classification === 'detail') {
			// Apply automatic category prefix if available
			const originalDescription = getRowStringValue(row, 'description', mapping);
			if (currentCategoryPrefix) {
				classifiedRow.categoryPrefix = currentCategoryPrefix;
				classifiedRow.finalDescription = currentCategoryPrefix + ' ' + originalDescription;
			} else {
				classifiedRow.finalDescription = originalDescription;
			}
		} else if (classification === 'ignore') {
			const description = getRowStringValue(row, 'description', mapping);
			if (!description.trim()) {
				// Blank row - reset automatic category prefix
				currentCategoryPrefix = '';
			}
		}

		result.push(classifiedRow);
	}

	return result;
}

/**
 * Validate that required columns are mapped
 */
export function validateColumnMapping(mapping: ColumnMapping): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];

	// Check required and conditionally required fields
	for (const field of APPLICATION_FIELDS) {
		const isRequired =
			field.required || (field.conditionallyRequired && field.conditionallyRequired(mapping));

		if (isRequired && mapping[field.key] === undefined) {
			errors.push(`${field.label} is required`);
		}
	}

	// Check for duplicate mappings
	const usedIndices = new Set<number>();
	for (const [_column, index] of Object.entries(mapping)) {
		if (index !== undefined) {
			if (usedIndices.has(index)) {
				errors.push(`Column index ${index} is mapped to multiple fields`);
			}
			usedIndices.add(index);
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

/**
 * Transform classified rows into import data format using column mapping
 */
export function transformToImportData(
	classifiedRows: ClassifiedRow[],
	mapping: ColumnMapping,
	projectId: string,
): { project_id: string; items: ImportItem[] } {
	const detailRows = classifiedRows.filter((row) => row.classification === 'detail');
	const appliedFactors: Record<string, number> = {};

	const items = detailRows.map((row) => {
		const code = getRowStringValue(row, 'code', mapping);
		const description = row.finalDescription || getRowStringValue(row, 'description', mapping);
		const quantity = getRowNumericValue(row, 'quantity', mapping);
		const unit = getRowStringValue(row, 'uom', mapping);
		const factor = getRowNumericValue(row, 'factor', mapping, 1);

		// parentFactor will be calculated in third pass after factors are properly assigned
		const parentFactor: number | undefined = undefined;

		// Handle different rate fields based on mapping
		let material_rate = 0;
		let labor_rate: number | undefined;
		let productivity_per_hour: number | undefined;
		let unit_rate_manual_override = false;
		let unit_rate: number | undefined;

		// Check if we have specific rate fields mapped
		if (mapping.material_rate !== undefined) {
			material_rate = getRowNumericValue(row, 'material_rate', mapping);
		}
		if (mapping.labor_rate !== undefined) {
			labor_rate = getRowNumericValue(row, 'labor_rate', mapping);
		}
		if (mapping.productivity_per_hour !== undefined) {
			productivity_per_hour = getRowNumericValue(row, 'productivity_per_hour', mapping);
		}
		if (mapping.unit_rate_manual_override !== undefined) {
			unit_rate_manual_override = Boolean(getRowValue(row, 'unit_rate_manual_override', mapping));
		}
		if (mapping.unit_rate !== undefined) {
			unit_rate = getRowNumericValue(row, 'unit_rate', mapping);
		}

		// Handle legacy 'rate' field mapping to material_rate
		if (mapping.rate !== undefined && material_rate === 0) {
			material_rate = getRowNumericValue(row, 'rate', mapping);
		}

		const subtotal = getRowNumericValue(row, 'subtotal', mapping);
		if (quantity === 0 && subtotal > 0 && !unit_rate_manual_override) {
			// This is a subtotal-only row - set quantity to 1 and use subtotal as unit rate
			return {
				code,
				description,
				quantity: 1,
				unit: unit ?? '',
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override: true,
				unit_rate: subtotal,
				factor,
				parentFactor,
				remarks: undefined,
			};
		}
		const sumOfTotals = getSumOfTotals(row, mapping);
		if (quantity === 0 && sumOfTotals > 0 && !unit_rate_manual_override) {
			// This is a subtotal- or total-only row - set quantity to 1 and use subtotal as unit rate
			return {
				code,
				description,
				quantity: 1,
				unit: unit ?? '',
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override: true,
				unit_rate: sumOfTotals,
				factor,
				parentFactor,
				remarks: undefined,
			};
		}

		if (code && description && !quantity && !material_rate && !subtotal && !sumOfTotals) {
			// This is a category row - factor will be assigned in second pass
			appliedFactors[code] = factor;

			return {
				code,
				description,
				quantity,
				unit,
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override: false,
				unit_rate,
				factor,
				parentFactor,
				remarks: undefined,
			};
		}

		return {
			code,
			description,
			quantity: quantity ?? 1, // Default to 1 if no quantity
			unit: unit ?? '',
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			parentFactor,
			remarks: undefined,
		};
	});

	// Second pass: Apply hierarchical factor assignment using stack-based approach
	assignFactorsFromSummaryRows(items, classifiedRows, mapping, appliedFactors);

	// Third pass: Calculate parentFactor for all items using the correct factors
	calculateParentFactors(items, appliedFactors);

	return {
		project_id: projectId,
		items,
	};
}

/**
 * Assign factors from summary rows using a stack-based approach to handle nested categories
 * with identical descriptions correctly
 */
function assignFactorsFromSummaryRows(
	items: ImportItem[],
	classifiedRows: ClassifiedRow[],
	mapping: ColumnMapping,
	appliedFactors: Record<string, number>,
): void {
	// Create a map of WBS codes to their corresponding items for quick lookup
	const itemsByCode = new Map<string, ImportItem>();
	for (const item of items) {
		itemsByCode.set(item.code, item);
	}

	// Stack to track summary rows as we process the spreadsheet from bottom to top
	const summaryStack: Array<{ description: string; factor: number; originalIndex: number }> = [];

	// Process all rows from bottom to top to build the stack correctly
	for (let i = classifiedRows.length - 1; i >= 0; i--) {
		const row = classifiedRows[i];

		if (row.classification === 'summary') {
			const summaryDescription = getRowStringValue(row, 'description', mapping);
			if (summaryDescription && summaryDescription.trim().toUpperCase().startsWith('TOTAL ')) {
				const baseDescription = summaryDescription.trim().slice(6);
				const factor = getRowNumericValue(row, 'factor', mapping, 1);
				summaryStack.push({
					description: baseDescription,
					factor,
					originalIndex: row.originalIndex,
				});
			}
		} else if (row.classification === 'detail') {
			const code = getRowStringValue(row, 'code', mapping);
			const item = itemsByCode.get(code);

			// Check if this is a category row (has code and description but no quantity/rates)
			if (item && item.quantity === 0 && item.material_rate === 0) {
				const description = getRowStringValue(row, 'description', mapping);

				// Find and pop the matching summary from the stack
				for (let j = summaryStack.length - 1; j >= 0; j--) {
					const summary = summaryStack[j];
					if (
						summary.description.toLowerCase() === description.trim().toLowerCase() &&
						summary.originalIndex > row.originalIndex
					) {
						// Found matching summary - assign its factor and remove from stack
						item.factor = summary.factor;
						appliedFactors[code] = summary.factor;
						summaryStack.splice(j, 1);
						break;
					}
				}
			}
		}
	}
}

/**
 * Calculate parentFactor for all items using the correct factors from appliedFactors
 */
function calculateParentFactors(items: ImportItem[], appliedFactors: Record<string, number>): void {
	for (const item of items) {
		let parentFactor: number | undefined = undefined;
		let { parent_code } = parseWbsCode(item.code);

		while (parent_code) {
			const parentFactorValue = appliedFactors[parent_code];
			if (parentFactorValue !== undefined) {
				parentFactor = parentFactor ? parentFactor * parentFactorValue : parentFactorValue;
			}
			({ parent_code } = parseWbsCode(parent_code));
		}

		item.parentFactor = parentFactor;
	}
}
