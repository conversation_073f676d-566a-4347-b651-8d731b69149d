import { z } from 'zod';

// Schema for stage completion
export const stageCompletionSchema = z.object({
	project_stage_id: z.string().uuid(),
	notes: z.string().optional(),
});

// Schema for stage readiness check
export const stageReadinessSchema = z.object({
	project_stage_id: z.string().uuid(),
});

// Schema for gateway stage information
export const gatewayStageInfoSchema = z.object({
	project_stage_id: z.string().uuid(),

	// Floor Areas section
	basement_floors: z.number().optional().nullable(),
	ground_floor: z.number().optional().nullable(),
	upper_floors: z.number().optional().nullable(),
	total_gross_internal_floor_area: z.number().optional().nullable(),
	usable_area: z.number().optional().nullable(),
	circulation_area: z.number().optional().nullable(),
	ancillary_areas: z.number().optional().nullable(),
	internal_divisions: z.number().optional().nullable(),
	spaces_not_enclosed: z.number().optional().nullable(),
	total_gross_internal_floor_area_2: z.number().optional().nullable(),
	internal_cube: z.number().optional().nullable(),
	area_of_lowest_floor: z.number().optional().nullable(),
	site_area: z.number().optional().nullable(),
	number_of_units: z.number().optional().nullable(),

	// Storeys section
	nr_of_storeys: z.number().optional().nullable(),
	nr_of_storeys_primary: z.number().optional().nullable(),
	nr_of_storeys_secondary: z.number().optional().nullable(),
	basement_storeys_included_above: z.number().optional().nullable(),
	average_storey_height: z.number().optional().nullable(),
	below_ground_floors: z.number().optional().nullable(),
	ground_floor_height: z.number().optional().nullable(),
	above_ground_floors: z.number().optional().nullable(),
	external_vertical_envelope: z.number().optional().nullable(),

	// Additional data as an array of tuples with [id, name, value]
	additional_data: z
		.array(
			z.tuple([
				z.string().length(11, { message: 'ID must be 11 characters long' }), // Unique identifier
				z.string().min(1, { message: 'Name is required' }), // Key/name
				z.string(), // Value
			]),
		)
		.optional()
		.nullable(),
});

// Schema for gateway stage information with ID (for updates)
export const gatewayStageInfoWithIdSchema = gatewayStageInfoSchema.extend({
	project_gateway_stage_info_id: z.string().uuid(),
});

// Schema for qualitative scorecard
export const qualitativeScorecardSchema = z.object({
	project_stage_id: z.string().uuid(),
	scorecard: z
		.array(
			z.array(
				z.union([
					z.string(), // For dimension names and criteria names
					z.number().min(0).max(10).optional().nullable(), // For scores (0-10)
				]),
			),
		)
		.optional()
		.nullable(),
});

export type GatewayStageInfo = z.infer<typeof gatewayStageInfoSchema>;
export type GatewayStageInfoWithId = z.infer<typeof gatewayStageInfoWithIdSchema>;
export type QualitativeScorecard = z.infer<typeof qualitativeScorecardSchema>;
export type StageCompletion = z.infer<typeof stageCompletionSchema>;
export type StageReadiness = z.infer<typeof stageReadinessSchema>;
